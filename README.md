# 蛋白质分析平台 (Protein Analysis Platform)

一个基于 Vue.js + Node.js + SQLite 的现代化蛋白质分析平台，提供完整的用户管理、文件上传、分析处理和报告管理功能。

## 🚀 快速开始

### 前置要求

- Node.js 18+
- npm 或 yarn
- Docker (可选，用于容器化部署)

### 安装和启动

1. **克隆项目**

   ```bash
   git clone <repository-url>
   cd qtx
   ```

2. **安装依赖**

   ```bash
   cd web && npm install && cd ..
   cd apiserver && npm install && cd ..
   ```

3. **启动开发环境**

   ```bash
   # 启动后端服务器
   cd apiserver && npm start

   # 启动前端开发服务器
   cd web && npm run dev
   ```

4. **访问应用**
   - 前端: http://localhost:5173
   - 后端 API: http://localhost:3001

### 默认管理员账户

- 用户名: `admin`
- 密码: `fHadmin`

## 📁 项目结构

```
qtx/
├── web/                   # 前端源码 (Vue.js)
│   ├── src/              # Vue.js 源码
│   │   ├── views/        # 页面组件
│   │   ├── components/   # 通用组件
│   │   ├── stores/       # Pinia 状态管理
│   │   └── services/     # API 服务
│   ├── public/           # 静态资源
│   └── package.json      # 前端依赖
├── apiserver/            # 后端源码 (Node.js)
│   ├── routes/           # API 路由
│   ├── models/           # 数据模型
│   ├── middleware/       # 中间件
│   ├── uploads/          # 文件上传目录
│   └── package.json      # 后端依赖
├── docs/                 # 项目文档
├── scripts/              # 部署和工具脚本
├── docker/               # Docker 配置
└── tmp/                  # 临时文件
```

## 🔧 主要功能

### 用户功能

- ✅ 用户注册和登录
- ✅ 文件上传和管理
- ✅ 分析申请提交
- ✅ 报告查看和下载
- ✅ 个人资料管理

### 管理员功能

- ✅ 用户管理
- ✅ 申请处理
- ✅ 报告上传和管理
- ✅ 系统监控
- ✅ 数据统计

### 技术特性

- ✅ 响应式设计
- ✅ 多语言支持 (中文/英文)
- ✅ JWT 身份验证
- ✅ 文件上传和存储
- ✅ 数据库迁移
- ✅ Docker 容器化

## 📚 文档

详细文档请查看 `docs/` 目录：

- [快速开始指南](docs/QUICK_START.md)
- [部署指南](docs/DEPLOYMENT_GUIDE.md)
- [API 文档](server/API_DOCUMENTATION.md)
- [项目完成报告](docs/FINAL_COMPLETION_REPORT.md)

## 🛠️ 开发

### 开发命令

```bash
# 前端开发
cd web && npm run dev

# 后端开发
cd apiserver && npm start

# 运行测试
cd web && npm run test

# 构建生产版本
cd web && npm run build

# 代码检查
cd web && npm run lint
```

### Docker 部署

```bash
# 开发环境
docker-compose -f docker/docker-compose.dev.yml up

# 生产环境
docker-compose -f docker/docker-compose.yml up
```

## 🔄 数据库

项目使用 SQLite 数据库，支持自动迁移和数据初始化。

### 重置数据库

```bash
cd scripts
./reset-database.sh  # Linux/Mac
reset-database.bat   # Windows
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

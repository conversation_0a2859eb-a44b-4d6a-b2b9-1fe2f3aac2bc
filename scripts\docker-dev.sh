#!/bin/bash
# Docker 开发环境快速启动脚本
# 用途: 启动适合开发的Docker环境，支持热重载
# 作者: Quantix 开发团队
# 日期: 2025-06-19

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🐳 Quantix Docker 开发环境启动脚本${NC}"
echo "=================================================="

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker 未安装或未在PATH中${NC}"
    echo "请安装Docker: https://docs.docker.com/get-docker/"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose 未安装或未在PATH中${NC}"
    echo "请安装Docker Compose: https://docs.docker.com/compose/install/"
    exit 1
fi

# 检查Docker是否运行
if ! docker info &> /dev/null; then
    echo -e "${RED}❌ Docker 服务未运行${NC}"
    echo "请启动Docker服务"
    exit 1
fi

echo -e "${GREEN}✅ Docker 环境检查通过${NC}"

# 检查项目根目录
if [ ! -f "package.json" ] || [ ! -d "server" ] || [ ! -d "src" ]; then
    echo -e "${RED}❌ 请从项目根目录运行此脚本${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 项目目录检查通过${NC}"

# 停止现有容器
echo -e "${YELLOW}🛑 停止现有容器...${NC}"
docker-compose -f docker/docker-compose.yml down 2>/dev/null || true

# 清理旧的开发数据（可选）
read -p "是否清理旧的开发数据？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}🧹 清理开发数据...${NC}"
    docker volume rm quantix_mysql_dev_data 2>/dev/null || true
fi

# 构建镜像
echo -e "${YELLOW}🏗️ 构建开发环境镜像...${NC}"
docker-compose -f docker/docker-compose.yml -f docker/docker-compose.dev.yml build

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 镜像构建失败${NC}"
    exit 1
fi

# 启动开发环境
echo -e "${YELLOW}🚀 启动开发环境...${NC}"
docker-compose -f docker/docker-compose.yml -f docker/docker-compose.dev.yml up -d

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 容器启动失败${NC}"
    echo "查看错误日志: docker-compose -f docker/docker-compose.yml logs"
    exit 1
fi

echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
sleep 15

# 检查服务状态
echo -e "${BLUE}🔍 检查服务状态...${NC}"
docker-compose -f docker/docker-compose.yml ps

# 等待数据库就绪
echo -e "${YELLOW}⏳ 等待数据库就绪...${NC}"
for i in {1..30}; do
    if docker-compose -f docker/docker-compose.yml exec -T database mysqladmin ping -h localhost --silent; then
        echo -e "${GREEN}✅ 数据库已就绪${NC}"
        break
    fi
    if [ $i -eq 30 ]; then
        echo -e "${RED}❌ 数据库启动超时${NC}"
        exit 1
    fi
    sleep 2
done

# 健康检查
echo -e "${BLUE}🏥 执行健康检查...${NC}"

# 检查后端健康
if curl -f http://localhost:3001/api/health &> /dev/null; then
    echo -e "${GREEN}✅ 后端服务健康${NC}"
else
    echo -e "${YELLOW}⚠️ 后端服务可能还在启动中${NC}"
fi

# 检查前端
if curl -f http://localhost:5173 &> /dev/null; then
    echo -e "${GREEN}✅ 前端服务健康${NC}"
else
    echo -e "${YELLOW}⚠️ 前端服务可能还在启动中${NC}"
fi

echo ""
echo -e "${GREEN}🎉 开发环境启动完成！${NC}"
echo "=================================================="
echo -e "${BLUE}📋 服务信息:${NC}"
echo "  🌐 前端开发服务器: http://localhost:5173"
echo "  🔧 后端API服务器:  http://localhost:3001"
echo "  🗄️ 数据库:         localhost:3306"
echo "  📊 Nginx状态:      http://localhost/nginx-status"
echo ""
echo -e "${BLUE}🛠️ 开发工具:${NC}"
echo "  📝 查看日志: docker-compose -f docker/docker-compose.yml logs -f"
echo "  🔍 进入容器: docker-compose -f docker/docker-compose.yml exec [service] sh"
echo "  🛑 停止服务: docker-compose -f docker/docker-compose.yml down"
echo ""
echo -e "${BLUE}💡 开发特性:${NC}"
echo "  🔄 热重载: 前端和后端代码修改会自动重载"
echo "  🗄️ 数据持久化: 数据库数据保存在Docker卷中"
echo "  🐛 调试模式: 后端运行在调试模式"
echo ""
echo -e "${YELLOW}⚠️ 注意: 这是开发环境配置，不适用于生产环境${NC}"

# 可选：打开浏览器
read -p "是否打开浏览器？(Y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Nn]$ ]]; then
    if command -v xdg-open &> /dev/null; then
        xdg-open http://localhost:5173
    elif command -v open &> /dev/null; then
        open http://localhost:5173
    else
        echo "请手动打开浏览器访问: http://localhost:5173"
    fi
fi

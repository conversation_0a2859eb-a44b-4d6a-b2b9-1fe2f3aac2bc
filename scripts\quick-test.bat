@echo off
setlocal enabledelayedexpansion

echo 🚀 Quick Test - Quantix Platform
echo.

REM Check if dependencies are installed
if not exist "node_modules" (
    echo 📦 Installing frontend dependencies...
    npm install
)

if not exist "server\node_modules" (
    echo 📦 Installing backend dependencies...
    cd server
    npm install
    cd ..
)

REM Create basic environment files
if not exist "server\.env" (
    echo 📝 Creating basic server environment...
    (
        echo NODE_ENV=development
        echo PORT=3001
        echo FRONTEND_URL=http://localhost:5173
        echo DB_HOST=localhost
        echo DB_PORT=3306
        echo DB_NAME=quantix
        echo DB_USER=root
        echo DB_PASSWORD=
        echo JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-2024
        echo JWT_EXPIRE=7d
        echo EMAIL_SERVICE=gmail
        echo EMAIL_USER=<EMAIL>
        echo EMAIL_PASS=your-app-password
        echo OPENAI_API_KEY=your-openai-api-key-here
        echo OPENAI_MODEL=gpt-3.5-turbo
        echo MAX_FILE_SIZE=104857600
        echo UPLOAD_PATH=./uploads
    ) > server\.env
)

if not exist ".env" (
    echo 📝 Creating frontend environment...
    echo VITE_API_BASE_URL=http://localhost:3001/api > .env
)

REM Create uploads directory
if not exist "server\uploads" (
    mkdir "server\uploads"
)

echo 🔧 Starting backend server...
start "Quantix Backend" cmd /k "cd server && npm run dev"

echo ⏳ Waiting for backend to start...
timeout /t 5 /nobreak > nul

echo 🎨 Starting frontend server...
start "Quantix Frontend" cmd /k "npm run dev"

echo ⏳ Waiting for frontend to start...
timeout /t 5 /nobreak > nul

echo.
echo 🎉 Quantix Platform is starting!
echo.
echo 📊 Frontend: http://localhost:5173
echo 🔧 Backend API: http://localhost:3001/api
echo.
echo ⚠️  Note: Some features may not work without proper database setup.
echo    For full functionality, please configure MySQL and run the complete setup.
echo.
echo Press any key to open the application...
pause > nul

start http://localhost:5173

echo.
echo ✅ Application opened in browser!
echo.
echo To stop the services, close the backend and frontend terminal windows.
pause

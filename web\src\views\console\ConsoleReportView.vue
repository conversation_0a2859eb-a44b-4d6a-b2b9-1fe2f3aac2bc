<template>
  <div class="report-container">
    <header class="report-header">
      <h1>{{ $t('reportPreview') }} - {{ $t('applicationNumber') }}: {{ $route.params.id }}</h1>
      <button @click="goBack" class="btn-secondary">{{ $t('backToDashboard') }}</button>
    </header>

    <main class="report-main">
      <div class="report-preview-card">
        <h2>{{ $t('reportSummary') }}</h2>
        <p>{{ $t('reportSummaryDesc') }}</p>
        <p>
          <strong>{{ $t('analysisResultsOverview') }}:</strong>
        </p>
        <ul>
          <li>{{ $t('proteinInteractionNetworkAnalysis') }}</li>
          <li>{{ $t('complexStructurePrediction') }}</li>
          <li>{{ $t('functionalEnrichmentAnalysis') }}</li>
          <li>{{ $t('potentialDrugTargetIdentification') }}</li>
        </ul>
        <p>{{ $t('fullReportPrompt') }}</p>
        <button @click="downloadFullReport" class="btn-primary">
          {{ $t('payToDownloadFullReport') }}
        </button>
      </div>
    </main>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { applicationAPI } from '@/services/api'

export default defineComponent({
  name: 'ConsoleReportView',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const { t } = useI18n()

    const applicationData = ref<any>(null)
    const loading = ref(true)
    const errorMessage = ref('')

    const goBack = () => {
      router.push('/console')
    }

    const downloadFullReport = () => {
      // 跳转到支付页面，传递应用ID和价格
      const price = applicationData.value?.reportPrice || 0
      router.push({
        name: 'console-payment',
        query: {
          applicationId: route.params.id,
          amount: price,
          title: applicationData.value?.title || t('analysisReport')
        }
      })
    }

    const fetchApplicationData = async () => {
      try {
        loading.value = true
        const response = await applicationAPI.getApplication(Number(route.params.id))
        if (response.data.success) {
          applicationData.value = response.data.application
        } else {
          errorMessage.value = t('loadingText')
        }
      } catch (error: any) {
        console.error('Failed to fetch application:', error)
        errorMessage.value = error.response?.data?.message || t('loadingText')
      } finally {
        loading.value = false
      }
    }

    onMounted(() => {
      fetchApplicationData()
    })

    return {
      goBack,
      downloadFullReport,
      applicationData,
      loading,
      errorMessage,
    }
  },
})
</script>

<style scoped>
.report-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  font-family: 'Arial', sans-serif;
  background-color: #f4f7f6;
}

.report-header {
  background-color: #007bff;
  color: white;
  padding: 20px 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.report-header h1 {
  margin: 0;
  font-size: 28px;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.report-main {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

.report-preview-card {
  background-color: #ffffff;
  padding: 40px;
  border-radius: 10px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  text-align: center;
  width: 100%;
  max-width: 700px;
}

.report-preview-card h2 {
  color: #333;
  margin-bottom: 20px;
  font-size: 24px;
}

.report-preview-card p {
  color: #555;
  line-height: 1.8;
  margin-bottom: 15px;
  text-align: left;
}

.report-preview-card ul {
  list-style: disc inside;
  text-align: left;
  margin-bottom: 20px;
  color: #555;
}

.report-preview-card ul li {
  margin-bottom: 5px;
}

.btn-primary {
  background-color: #28a745;
  color: white;
  padding: 15px 30px;
  border: none;
  border-radius: 5px;
  font-size: 20px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-top: 20px;
}

.btn-primary:hover {
  background-color: #218838;
}
</style>

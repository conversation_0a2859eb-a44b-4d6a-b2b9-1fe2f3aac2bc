@echo off
setlocal enabledelayedexpansion

echo 🚀 启动 Quantix 平台 - 生产模式
echo.

REM 设置环境变量
set NODE_ENV=production
set PORT=3001
set FRONTEND_URL=http://localhost:3001

echo 📦 检查依赖...
if not exist "node_modules" (
    echo 安装前端依赖...
    npm install
)

if not exist "server\node_modules" (
    echo 安装后端依赖...
    cd server
    npm install
    cd ..
)

echo 🏗️ 构建前端项目...
npm run build
if %errorlevel% neq 0 (
    echo ❌ 前端构建失败
    pause
    exit /b 1
)

echo ✅ 前端构建完成

REM 创建uploads目录
if not exist "server\uploads" (
    mkdir "server\uploads"
)

echo 🗄️ 初始化数据库...
cd server
node scripts/init-database.js
if %errorlevel% neq 0 (
    echo ⚠️ 数据库初始化可能有问题，但继续启动...
)

echo 🔧 启动后端服务器...
start "Quantix Backend" cmd /k "node index.js"

cd ..

echo ⏳ 等待后端启动...
timeout /t 5 /nobreak > nul

echo.
echo 🎉 Quantix 平台启动完成！
echo.
echo 📊 访问地址: http://localhost:3001
echo 🔐 管理员登录: http://localhost:3001/admin/login
echo 📚 API文档: http://localhost:3001/api/health
echo.
echo 🔑 默认管理员账户:
echo    用户名: admin
echo    密码: fHadmin
echo    邮箱: <EMAIL>
echo.
echo Press any key to open the application...
pause > nul

start http://localhost:3001

echo.
echo ✅ 应用已在浏览器中打开！
echo.
echo 要停止服务，请关闭后端终端窗口。
pause

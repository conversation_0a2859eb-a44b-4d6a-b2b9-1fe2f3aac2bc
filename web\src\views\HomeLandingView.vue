<template>
  <div class="home-landing-container">
    <header class="navbar">
      <div class="navbar-left">
        <div class="logo">{{ $t('welcome') }}</div>
      </div>
      <div class="navbar-right">
        <nav>
          <ul>
            <li>
              <a href="#features">{{ $t('features') }}</a>
            </li>
            <li>
              <a href="#about">{{ $t('aboutUs') }}</a>
            </li>
            <li>
              <a href="#contact">{{ $t('contactUs') }}</a>
            </li>
            <li class="auth-buttons">
              <button @click="goToLogin" class="btn-login">
                {{ $t('login') }} / {{ $t('register') }}
              </button>
            </li>
            <li class="language-switcher">
              <select v-model="currentLocale" @change="changeLanguage">
                <option value="en">English</option>
                <option value="zh">中文</option>
                <option value="es">Español</option>
                <option value="fr">Français</option>
                <option value="de">Deutsch</option>
                <option value="ja">日本語</option>
                <option value="ko">한국어</option>
              </select>
            </li>
          </ul>
        </nav>
      </div>
    </header>

    <main class="hero-section">
      <div class="hero-content">
        <h1>{{ $t('heroTitle') }}</h1>
        <p>{{ $t('heroSubtitle') }}</p>
        <button @click="goToLogin" class="btn-hero">{{ $t('startAnalysis') }}</button>
      </div>
    </main>

    <section id="features" class="features-section">
      <h2>{{ $t('coreFeatures') }}</h2>
      <div class="feature-grid">
        <div class="feature-item">
          <h3>{{ $t('highThroughput') }}</h3>
          <p>{{ $t('highThroughputDesc') }}</p>
        </div>
        <div class="feature-item">
          <h3>{{ $t('smartAlgorithm') }}</h3>
          <p>{{ $t('smartAlgorithmDesc') }}</p>
        </div>
        <div class="feature-item">
          <h3>{{ $t('visualReport') }}</h3>
          <p>{{ $t('visualReportDesc') }}</p>
        </div>
      </div>
    </section>

    <section id="qa-section" class="qa-section">
      <h2>{{ $t('qaTitle') }}</h2>
      <p>{{ $t('qaSubtitle') }}</p>
      <button @click="goToChat" class="btn-qa">{{ $t('askNow') }}</button>
    </section>

    <section id="about" class="about-section">
      <h2>{{ $t('aboutUs') }}</h2>
      <p>{{ $t('aboutUsDesc') }}</p>
    </section>

    <section id="contact" class="contact-section">
      <h2>{{ $t('contactUs') }}</h2>
      <p>{{ $t('phone') }}: +86-123-4567-890</p>
      <p>{{ $t('email') }}: <EMAIL></p>
      <p>{{ $t('address') }}: {{ $t('addressDetail') }}</p>
    </section>

    <footer class="footer">
      <p>&copy; 2023 {{ $t('welcome') }}. {{ $t('allRightsReserved') }}.</p>
      <div class="admin-link">
        <router-link v-if="systemInitialized" to="/login" class="admin-login-link">🔐 登录</router-link>
        <router-link v-else to="/system/init" class="admin-login-link init-link">🚀 初始化系统</router-link>
      </div>
    </footer>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
// Dynamic import for systemCheck to avoid bundling conflicts
import { useLanguage } from '@/utils/language'

export default defineComponent({
  name: 'HomeLandingView',
  setup() {
    const router = useRouter()
    const { locale, changeLanguage: setLanguage } = useLanguage()

    const currentLocale = ref(locale.value)
    const systemInitialized = ref(true) // 默认为true，避免闪烁

    const changeLanguage = (event: Event) => {
      const target = event.target as HTMLSelectElement
      const newLocale = target.value
      setLanguage(newLocale)
      currentLocale.value = newLocale
    }

    const goToLogin = () => {
      router.push('/login')
    }

    const goToChat = () => {
      router.push('/chat')
    }

    // 检查系统初始化状态
    onMounted(async () => {
      try {
        const { checkSystemInitialization } = await import('@/utils/systemCheck')
        const status = await checkSystemInitialization()
        systemInitialized.value = status.isInitialized
      } catch (error) {
        console.error('Failed to check system status:', error)
        // 如果检查失败，默认显示初始化链接
        systemInitialized.value = false
      }
    })

    return {
      goToLogin,
      goToChat,
      currentLocale,
      changeLanguage,
      systemInitialized
    }
  },
})
</script>

<style scoped>
html,
body {
  background-color: transparent; /* 确保html和body没有默认背景色 */
}

.home-landing-container {
  font-family: 'Arial', sans-serif;
  color: #333;
  background-color: transparent; /* 确保容器没有默认背景色 */
  position: relative; /* 添加相对定位 */
  min-height: 100vh; /* 确保容器至少占据整个视口的高度 */
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  width: calc(100% - 40px); /* 减去左右padding */
  box-sizing: border-box; /* 确保padding不增加宽度 */
  background-color: transparent !important; /* 移除背景色并强制透明 */
  box-shadow: none; /* 移除阴影 */
  position: fixed; /* 固定定位，确保在顶部 */
  width: 100%;
  top: 0;
  left: 0;
  z-index: 1001; /* 提高z-index */
}

.navbar .logo {
  font-size: 24px;
  font-weight: bold;
  color: white; /* 调整颜色以适应背景 */
}

.navbar-left {
  display: flex;
  align-items: center;
}

.navbar-right {
  display: flex;
  align-items: center;
}

.navbar nav ul {
  list-style: none;
  display: flex;
  margin: 0;
  padding: 0;
  gap: 20px; /* 增加导航项之间的间距 */
  align-items: center; /* 垂直居中对齐所有导航项 */
}

.navbar nav ul li {
  margin-left: 0; /* 移除原有的左边距 */
  display: flex;
  align-items: center;
}

.navbar nav ul li a {
  color: white;
  text-decoration: none;
  padding: 8px 12px;
  border-radius: 5px;
  transition: background-color 0.3s ease, border 0.3s ease;
  border: 1px solid transparent; /* 默认透明边框 */
  background-color: transparent; /* 默认透明背景 */
  display: flex; /* 使用flexbox进行内容对齐 */
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
}

.navbar nav ul li a:hover {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.5); /* 悬停时显示边框 */
}

.navbar nav ul li.auth-buttons {
  margin-left: 0; /* 调整登录/注册按钮的左边距 */
}

.navbar nav ul li.language-switcher {
  margin-left: 0; /* 调整语言切换器的左边距 */
}

.language-switcher select {
  background-color: transparent;
  color: white;
  padding: 8px 12px; /* 统一内边距 */
  border: 1px solid white; /* 默认白色边框 */
  border-radius: 5px;
  appearance: none; /* 移除默认选择框样式 */
  cursor: pointer;
  text-align: center; /* 文字居中 */
  text-align-last: center; /* 确保在所有浏览器中居中 */
  display: block; /* 确保块级显示以便text-align生效 */
  transition: background-color 0.3s ease, border 0.3s ease;
}

.language-switcher select:hover {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.5); /* 悬停时显示边框 */
}

.language-switcher select option {
  color: #333; /* 选项文字颜色 */
  background-color: #fff; /* 选项背景颜色 */
}

.btn-login {
  background-color: transparent; /* 默认透明背景 */
  color: white;
  padding: 8px 12px; /* 统一内边距 */
  border: 1px solid white; /* 默认白色边框 */
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease, border 0.3s ease;
  display: flex; /* 使用flexbox进行内容对齐 */
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
}

.btn-login:hover {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.5); /* 悬停时显示边框 */
}

/* 手机端适配 */
@media (max-width: 768px) {
  .navbar {
    flex-direction: column; /* 垂直堆叠 */
    align-items: flex-start; /* 左对齐 */
    padding: 10px 15px;
  }

  .navbar-right {
    width: 100%;
    margin-top: 10px;
  }

  .navbar nav ul {
    flex-direction: column; /* 垂直堆叠导航项 */
    width: 100%;
    gap: 10px; /* 减小间距 */
  }

  .navbar nav ul li {
    width: 100%;
  }

  .navbar nav ul li a,
  .btn-login,
  .language-switcher select {
    width: 100%; /* 宽度占满 */
    text-align: center; /* 文字居中 */
    box-sizing: border-box; /* 确保padding不增加宽度 */
    justify-content: center; /* 手机端水平居中 */
  }
}

.btn-login:hover {
  background-color: rgba(255, 255, 255, 0.2); /* 调整悬停背景色 */
  color: white;
}

.hero-section {
  background: url('@/assets/images/FPM.005.png') no-repeat center center/cover;
  height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: white;
  position: fixed; /* 更改为固定定位 */
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 0; /* 将z-index设置为0，确保在所有内容之下 */
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3); /* 调整遮罩层透明度 */
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  padding: 20px;
  padding-top: 0; /* 调整内边距 */
}

.hero-content h1 {
  font-size: 56px;
  margin-bottom: 20px;
  font-weight: bold;
}

.hero-content p {
  font-size: 22px;
  margin-bottom: 40px;
  line-height: 1.5;
}

.btn-hero {
  background-color: #4caf50; /* 调整按钮颜色 */
  color: white;
  padding: 15px 35px;
  border: none;
  border-radius: 8px;
  font-size: 20px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-hero:hover {
  background-color: #45a049; /* 调整按钮悬停颜色 */
}

.features-section,
.about-section,
.contact-section,
.footer {
  padding-top: 80px; /* 调整各section的顶部内边距 */
}

.home-landing-container {
  overflow-x: hidden;
}

.hero-section {
  background: url('@/assets/images/FPM.005.png') no-repeat center center/cover;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: white;
  position: relative; /* 更改为相对定位 */
  margin-top: 0; /* 移除顶部外边距 */
  padding-top: 60px; /* 调整内边距，为导航栏留出空间 */
  /* z-index: -1; 将z-index设置为负值，确保在所有内容之下 */
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5); /* 遮罩层 */
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  padding: 20px;
}

.hero-content h1 {
  font-size: 56px;
  margin-bottom: 20px;
  font-weight: bold;
}

.hero-content p {
  font-size: 22px;
  margin-bottom: 40px;
  line-height: 1.5;
}

.btn-hero {
  background-color: var(--color-success);
  color: white;
  padding: 15px 35px;
  border: none;
  border-radius: 8px;
  font-size: 20px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-hero:hover {
  background-color: #218838;
}

.features-section,
.about-section,
.contact-section {
  padding: 80px 50px;
  text-align: center;
  background-color: var(--color-background-white);
  margin-bottom: 20px;
  box-shadow: var(--shadow-light);
}

.features-section h2,
.about-section h2,
.contact-section h2 {
  font-size: 36px;
  margin-bottom: 50px;
  color: var(--color-primary);
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-item {
  background-color: var(--color-light);
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-10px);
}

.feature-item h3 {
  font-size: 24px;
  margin-bottom: 15px;
  color: var(--color-dark);
}

.feature-item p {
  font-size: 16px;
  color: var(--color-text-medium);
  line-height: 1.7;
}

.about-section p,
.contact-section p {
  font-size: 18px;
  line-height: 1.8;
  max-width: 800px;
  margin: 0 auto;
  color: var(--color-text-medium);
}

.footer {
  background-color: var(--color-dark);
  color: white;
  text-align: center;
  padding: 25px 0;
  font-size: 15px;
}

.admin-link {
  margin-top: 15px;
}

.admin-login-link {
  color: #ccc;
  text-decoration: none;
  font-size: 12px;
  padding: 5px 10px;
  border: 1px solid #555;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.admin-login-link:hover {
  color: white;
  border-color: #888;
  background-color: rgba(255, 255, 255, 0.1);
}

.init-link {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white !important;
  border-color: transparent !important;
  font-weight: 600;
}

.init-link:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
}

.qa-section {
  background-color: #f9f9f9;
  padding: 80px 50px;
  text-align: center;
}

.qa-section h2 {
  font-size: 36px;
  color: #333;
  margin-bottom: 20px;
}

.qa-section p {
  font-size: 18px;
  color: #666;
  margin-bottom: 40px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.btn-qa {
  background-color: #007bff;
  color: white;
  padding: 15px 30px;
  border: none;
  border-radius: 5px;
  font-size: 20px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-qa:hover {
  background-color: #0056b3;
}

.about-section {
  padding: 80px 50px;
  text-align: center;
  background-color: #fff;
}

.about-section h2 {
  font-size: 36px;
  color: #333;
  margin-bottom: 20px;
}

.about-section p {
  font-size: 18px;
  color: #666;
  line-height: 1.8;
  max-width: 900px;
  margin: 0 auto;
}

.contact-section {
  background-color: #f0f0f0;
  padding: 80px 50px;
  text-align: center;
}

.contact-section h2 {
  font-size: 36px;
  color: #333;
  margin-bottom: 20px;
}

.contact-section p {
  font-size: 18px;
  color: #666;
  margin-bottom: 10px;
}

.footer {
  background-color: #333;
  color: white;
  text-align: center;
  padding: 20px 0;
  font-size: 14px;
}
</style>

// TypeScript declarations for api.js

import { AxiosInstance } from 'axios'
import {
  AuthAPI,
  UserAPI,
  UploadAPI,
  ApplicationAPI,
  PaymentAPI,
  SystemAPI,
  AdminAPI,
  SetAuthTokenFunction,
  GetAuthTokenFunction,
  IsAuthenticatedFunction,
  HandleApiErrorFunction
} from '@/types/api'

// Default axios instance
declare const api: AxiosInstance

// API exports
export const authAPI: AuthAPI
export const userAPI: UserAPI
export const uploadAPI: UploadAPI
export const applicationAPI: ApplicationAPI
export const reportAPI: {
  getReportByApplication: (applicationId: number) => Promise<any>
  getReport: (reportId: number) => Promise<any>
  downloadReport: (reportId: number, format?: string) => Promise<any>
  getReports: (params?: any) => Promise<any>
}
export const chatAPI: {
  sendMessage: (data: any) => Promise<any>
  sendPublicMessage: (data: any) => Promise<any>
  getSuggestions: (language: string) => Promise<any>
  getChatHistory: (params?: any) => Promise<any>
  clearChatHistory: () => Promise<any>
  getChatStats: () => Promise<any>
  getChatHealth: () => Promise<any>
}
export const healthAPI: {
  getHealth: () => Promise<any>
}
export const paymentAPI: PaymentAPI
export const systemAPI: SystemAPI
export const adminAPI: AdminAPI

// Utility functions
export const setAuthToken: SetAuthTokenFunction
export const getAuthToken: GetAuthTokenFunction
export const isAuthenticated: IsAuthenticatedFunction
export const handleApiError: HandleApiErrorFunction

export default api

const bcrypt = require('bcryptjs');
const { User } = require('./server/models');

async function testHash() {
  const user = await User.findOne({ where: { email: '<EMAIL>' } });
  if (!user) {
    console.log('用户未找到');
    return;
  }
  
  console.log('用户找到:', user.email);
  console.log('密码哈希长度:', user.password.length);
  console.log('密码哈希:', user.password);
  
  const testPasswords = ['fHadmin', 'admin', 'password', '123456'];
  
  for (const pwd of testPasswords) {
    try {
      const result = await bcrypt.compare(pwd, user.password);
      console.log(`密码 "${pwd}": ${result}`);
    } catch (error) {
      console.log(`密码 "${pwd}": 错误 - ${error.message}`);
    }
  }
  
  // 测试comparePassword方法
  for (const pwd of testPasswords) {
    try {
      const result = await user.comparePassword(pwd);
      console.log(`方法测试 "${pwd}": ${result}`);
    } catch (error) {
      console.log(`方法测试 "${pwd}": 错误 - ${error.message}`);
    }
  }
  
  process.exit();
}

testHash();

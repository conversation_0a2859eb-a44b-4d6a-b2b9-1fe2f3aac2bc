# ============================================================================
# Quantix Frontend Environment Configuration Example
# ============================================================================
# Copy this file to .env and update the values according to your environment

# ============================================================================
# API Configuration
# ============================================================================
# Base URL for API calls (use /api for both Docker and development)
VITE_API_BASE_URL=/api

# ============================================================================
# Environment
# ============================================================================
# Environment mode: development, production, test
NODE_ENV=development

# ============================================================================
# Frontend Configuration
# ============================================================================
# Frontend URL (used for CORS and redirects)
VITE_FRONTEND_URL=http://localhost:5173

# ============================================================================
# Feature Flags
# ============================================================================
# Enable/disable development features
VITE_DEV_MODE=true
VITE_DEBUG_MODE=false

# ============================================================================
# Third-party Services
# ============================================================================
# Analytics (optional)
VITE_ANALYTICS_ID=

# Error tracking (optional)
VITE_SENTRY_DSN=

# ============================================================================
# Build Configuration
# ============================================================================
# Build output directory
VITE_BUILD_DIR=dist

# Public path for assets
VITE_PUBLIC_PATH=/

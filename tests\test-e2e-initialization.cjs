// 端到端测试：系统初始化和管理员登录
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const BASE_URL = 'http://localhost:3001';
const FRONTEND_URL = 'http://localhost:5173';

// 测试数据
const TEST_ADMIN = {
  username: 'admin',
  email: '<EMAIL>',
  password: 'fHadmin',
  firstName: 'Super',
  lastName: 'Admin'
};

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testAPI(method, endpoint, data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      }
    };
    
    if (data) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(`${BASE_URL}${endpoint}`, options);
    const result = await response.json();
    
    return {
      success: response.ok,
      status: response.status,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

async function runE2ETest() {
  log('blue', '🧪 开始端到端测试：系统初始化和管理员登录');
  log('blue', '='.repeat(60));
  
  let testsPassed = 0;
  let testsFailed = 0;
  
  // 测试1：检查系统初始状态
  log('yellow', '\n📋 测试1: 检查系统初始状态');
  const initStatus = await testAPI('GET', '/api/system/init-status');
  
  if (initStatus.success && !initStatus.data.isInitialized) {
    log('green', '✅ 系统未初始化，状态正确');
    log('blue', `   管理员数量: ${initStatus.data.adminCount}`);
    testsPassed++;
  } else {
    log('red', '❌ 系统状态检查失败');
    log('red', `   响应: ${JSON.stringify(initStatus)}`);
    testsFailed++;
  }
  
  // 测试2：系统初始化
  log('yellow', '\n🚀 测试2: 系统初始化');
  const initResult = await testAPI('POST', '/api/system/initialize', TEST_ADMIN);
  
  if (initResult.success) {
    log('green', '✅ 系统初始化成功');
    log('blue', `   创建用户: ${initResult.data.user.email}`);
    log('blue', `   用户角色: ${initResult.data.user.role}`);
    log('blue', `   用户名: ${initResult.data.username}`);
    testsPassed++;
  } else {
    log('red', '❌ 系统初始化失败');
    log('red', `   错误: ${JSON.stringify(initResult)}`);
    testsFailed++;
    return; // 如果初始化失败，后续测试无法进行
  }
  
  // 等待数据库写入
  await sleep(1000);
  
  // 测试3：检查初始化后状态
  log('yellow', '\n📊 测试3: 检查初始化后状态');
  const postInitStatus = await testAPI('GET', '/api/system/init-status');
  
  if (postInitStatus.success && postInitStatus.data.isInitialized && postInitStatus.data.adminCount === 1) {
    log('green', '✅ 初始化后状态正确');
    log('blue', `   管理员数量: ${postInitStatus.data.adminCount}`);
    testsPassed++;
  } else {
    log('red', '❌ 初始化后状态检查失败');
    log('red', `   响应: ${JSON.stringify(postInitStatus)}`);
    testsFailed++;
  }
  
  // 测试4：尝试重复初始化（应该失败）
  log('yellow', '\n🔒 测试4: 尝试重复初始化（应该失败）');
  const duplicateInit = await testAPI('POST', '/api/system/initialize', TEST_ADMIN);
  
  if (!duplicateInit.success && duplicateInit.status === 400) {
    log('green', '✅ 重复初始化被正确阻止');
    log('blue', `   错误信息: ${duplicateInit.data.message}`);
    testsPassed++;
  } else {
    log('red', '❌ 重复初始化应该被阻止');
    log('red', `   响应: ${JSON.stringify(duplicateInit)}`);
    testsFailed++;
  }
  
  // 测试5：管理员登录（使用邮箱）
  log('yellow', '\n🔐 测试5: 管理员登录（使用邮箱）');
  const emailLogin = await testAPI('POST', '/api/auth/login', {
    email: TEST_ADMIN.email,
    password: TEST_ADMIN.password
  });
  
  if (emailLogin.success && emailLogin.data.user.role === 'superadmin') {
    log('green', '✅ 邮箱登录成功');
    log('blue', `   用户: ${emailLogin.data.user.email}`);
    log('blue', `   角色: ${emailLogin.data.user.role}`);
    log('blue', `   Token: ${emailLogin.data.token ? '已生成' : '未生成'}`);
    testsPassed++;
  } else {
    log('red', '❌ 邮箱登录失败');
    log('red', `   响应: ${JSON.stringify(emailLogin)}`);
    testsFailed++;
  }
  
  // 测试6：错误密码登录（应该失败）
  log('yellow', '\n❌ 测试6: 错误密码登录（应该失败）');
  const wrongPasswordLogin = await testAPI('POST', '/api/auth/login', {
    email: TEST_ADMIN.email,
    password: 'wrongpassword'
  });
  
  if (!wrongPasswordLogin.success) {
    log('green', '✅ 错误密码登录被正确拒绝');
    log('blue', `   错误信息: ${wrongPasswordLogin.data.message}`);
    testsPassed++;
  } else {
    log('red', '❌ 错误密码登录应该被拒绝');
    log('red', `   响应: ${JSON.stringify(wrongPasswordLogin)}`);
    testsFailed++;
  }
  
  // 测试7：检查数据库中的用户
  log('yellow', '\n🗄️ 测试7: 检查数据库中的用户');
  try {
    const { User } = require('./server/models');
    const users = await User.findAll();
    
    if (users.length === 1 && users[0].email === TEST_ADMIN.email && users[0].role === 'superadmin') {
      log('green', '✅ 数据库中用户数据正确');
      log('blue', `   用户邮箱: ${users[0].email}`);
      log('blue', `   用户角色: ${users[0].role}`);
      log('blue', `   邮箱验证: ${users[0].isEmailVerified}`);
      testsPassed++;
    } else {
      log('red', '❌ 数据库中用户数据不正确');
      log('red', `   用户数量: ${users.length}`);
      if (users.length > 0) {
        log('red', `   第一个用户: ${JSON.stringify({
          email: users[0].email,
          role: users[0].role,
          isEmailVerified: users[0].isEmailVerified
        })}`);
      }
      testsFailed++;
    }
  } catch (error) {
    log('red', '❌ 数据库检查失败');
    log('red', `   错误: ${error.message}`);
    testsFailed++;
  }
  
  // 测试总结
  log('blue', '\n' + '='.repeat(60));
  log('blue', '📊 测试总结');
  log('green', `✅ 通过测试: ${testsPassed}`);
  log('red', `❌ 失败测试: ${testsFailed}`);
  log('blue', `📈 成功率: ${((testsPassed / (testsPassed + testsFailed)) * 100).toFixed(1)}%`);
  
  if (testsFailed === 0) {
    log('green', '\n🎉 所有测试通过！系统初始化和登录功能正常工作！');
    log('blue', '\n🌐 现在可以访问以下地址进行手动测试:');
    log('blue', `   主页: ${FRONTEND_URL}`);
    log('blue', `   初始化页面: ${FRONTEND_URL}/system/init`);
    log('blue', `   管理员登录: ${FRONTEND_URL}/admin/login`);
    log('blue', '\n🔑 使用以下凭据登录:');
    log('blue', `   邮箱: ${TEST_ADMIN.email}`);
    log('blue', `   密码: ${TEST_ADMIN.password}`);
  } else {
    log('red', '\n💥 有测试失败，需要修复问题！');
  }
  
  return testsFailed === 0;
}

// 运行测试
runE2ETest().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  log('red', `💥 测试运行失败: ${error.message}`);
  process.exit(1);
});

module.exports = { runE2ETest, TEST_ADMIN };

<script setup lang="ts">
import { RouterView } from 'vue-router'
import { onMounted } from 'vue'
import { useUserStore } from '@/stores/counter'
import { useLanguage } from '@/utils/language'

const userStore = useUserStore()
const { initializeLanguage } = useLanguage()

onMounted(() => {
  console.log('🚀 应用启动，初始化认证状态和语言设置')

  // 初始化语言设置
  initializeLanguage()

  // 初始化认证状态
  userStore.initializeAuth()

  console.log('✅ 认证状态和语言设置初始化完成', {
    isAuthenticated: userStore.isAuthenticated,
    user: userStore.user?.email,
  })
})
</script>

<template>
  <RouterView />
</template>

<style scoped>
/* 可以根据需要添加全局样式 */
</style>

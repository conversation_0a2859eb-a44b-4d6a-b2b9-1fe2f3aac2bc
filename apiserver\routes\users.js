const express = require('express');
const { body, validationResult } = require('express-validator');
const { User, sequelize } = require('../models');
const { authenticate, requireEmailVerification } = require('../middleware/auth');

const router = express.Router();

// Update user profile
router.patch('/profile',
  authenticate,
  [
    body('profile.firstName').optional().isString().trim().isLength({ max: 50 }).withMessage('First name must be less than 50 characters'),
    body('profile.lastName').optional().isString().trim().isLength({ max: 50 }).withMessage('Last name must be less than 50 characters'),
    body('profile.organization').optional().isString().trim().isLength({ max: 100 }).withMessage('Organization must be less than 100 characters'),
    body('profile.phone').optional().isMobilePhone('any').withMessage('Please provide a valid phone number'),
    body('profile.country').optional().isString().trim().isLength({ max: 50 }).withMessage('Country must be less than 50 characters')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const updates = {};
      if (req.body.profile) {
        Object.keys(req.body.profile).forEach(key => {
          if (req.body.profile[key] !== undefined) {
            updates[key] = req.body.profile[key];
          }
        });
      }

      await User.update(updates, { where: { id: req.user.id } });
      const user = await User.findByPk(req.user.id);

      res.json({
        message: 'Profile updated successfully',
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          organization: user.organization,
          phone: user.phone,
          country: user.country,
          language: user.language,
          subscriptionPlan: user.subscriptionPlan
        }
      });

    } catch (error) {
      console.error('Update profile error:', error);
      res.status(500).json({
        error: 'Update failed',
        message: 'An error occurred while updating profile'
      });
    }
  }
);

// Update user preferences
router.patch('/preferences',
  authenticate,
  [
    body('preferences.language').optional().isIn(['en', 'zh', 'es', 'fr', 'de', 'ja', 'ko']).withMessage('Invalid language'),
    body('preferences.notifications.email').optional().isBoolean().withMessage('Email notification preference must be boolean'),
    body('preferences.notifications.analysis').optional().isBoolean().withMessage('Analysis notification preference must be boolean')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const updates = {};
      if (req.body.preferences) {
        if (req.body.preferences.language) {
          updates.language = req.body.preferences.language;
        }
        if (req.body.preferences.notifications) {
          if (req.body.preferences.notifications.email !== undefined) {
            updates.emailNotifications = req.body.preferences.notifications.email;
          }
          if (req.body.preferences.notifications.analysis !== undefined) {
            updates.analysisNotifications = req.body.preferences.notifications.analysis;
          }
        }
      }

      await User.update(updates, { where: { id: req.user.id } });
      const user = await User.findByPk(req.user.id);

      res.json({
        message: 'Preferences updated successfully',
        preferences: {
          language: user.language,
          notifications: {
            email: user.emailNotifications,
            analysis: user.analysisNotifications
          }
        }
      });

    } catch (error) {
      console.error('Update preferences error:', error);
      res.status(500).json({
        error: 'Update failed',
        message: 'An error occurred while updating preferences'
      });
    }
  }
);

// Change password
router.patch('/password',
  authenticate,
  [
    body('currentPassword').notEmpty().withMessage('Current password is required'),
    body('newPassword')
      .isLength({ min: 6 })
      .withMessage('New password must be at least 6 characters long')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
      .withMessage('New password must contain at least one uppercase letter, one lowercase letter, and one number'),
    body('confirmPassword')
      .custom((value, { req }) => {
        if (value !== req.body.newPassword) {
          throw new Error('Password confirmation does not match new password');
        }
        return true;
      })
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { currentPassword, newPassword } = req.body;

      // Get user with password
      const user = await User.findByPk(req.user.id);

      // Verify current password
      const isCurrentPasswordValid = await user.comparePassword(currentPassword);
      if (!isCurrentPasswordValid) {
        return res.status(400).json({
          error: 'Invalid current password',
          message: 'The current password you entered is incorrect'
        });
      }

      // Update password
      user.password = newPassword;
      await user.save();

      res.json({
        message: 'Password changed successfully'
      });

    } catch (error) {
      console.error('Change password error:', error);
      res.status(500).json({
        error: 'Password change failed',
        message: 'An error occurred while changing password'
      });
    }
  }
);

// Get user statistics
router.get('/stats', authenticate, async (req, res) => {
  try {
    const { Application, Report } = require('../models');

    const [applicationStats, reportStats] = await Promise.all([
      Application.findAll({
        where: { userId: req.user.id, isDeleted: false },
        attributes: [
          'status',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
          [sequelize.fn('SUM', sequelize.col('actualCost')), 'totalCost']
        ],
        group: ['status'],
        raw: true
      }),
      Report.findAll({
        where: { userId: req.user.id },
        attributes: [
          'status',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['status'],
        raw: true
      })
    ]);

    const stats = {
      applications: {
        total: 0,
        pending: 0,
        processing: 0,
        completed: 0,
        failed: 0,
        cancelled: 0,
        totalCost: 0
      },
      reports: {
        total: 0,
        generating: 0,
        ready: 0,
        error: 0
      },
      account: {
        memberSince: req.user.createdAt,
        lastLogin: req.user.lastLogin,
        subscriptionPlan: req.user.subscriptionPlan
      }
    };

    // Process application stats
    applicationStats.forEach(stat => {
      stats.applications[stat.status] = parseInt(stat.count);
      stats.applications.total += parseInt(stat.count);
      stats.applications.totalCost += parseFloat(stat.totalCost) || 0;
    });

    // Process report stats
    reportStats.forEach(stat => {
      stats.reports[stat.status] = parseInt(stat.count);
      stats.reports.total += parseInt(stat.count);
    });

    res.json({ stats });

  } catch (error) {
    console.error('Get user stats error:', error);
    res.status(500).json({
      error: 'Failed to retrieve statistics',
      message: 'An error occurred while retrieving user statistics'
    });
  }
});

// Deactivate account
router.post('/deactivate',
  authenticate,
  requireEmailVerification,
  [
    body('password').notEmpty().withMessage('Password is required for account deactivation'),
    body('reason').optional().isString().isLength({ max: 500 }).withMessage('Reason must be less than 500 characters')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { password, reason } = req.body;

      // Get user with password
      const user = await User.findByPk(req.user.id);

      // Verify password
      const isPasswordValid = await user.comparePassword(password);
      if (!isPasswordValid) {
        return res.status(400).json({
          error: 'Invalid password',
          message: 'The password you entered is incorrect'
        });
      }

      // Deactivate account
      user.isActive = false;
      await user.save();

      // Log deactivation reason (in production, save to database)
      console.log(`User ${user.email} deactivated account. Reason: ${reason || 'Not provided'}`);

      res.json({
        message: 'Account deactivated successfully'
      });

    } catch (error) {
      console.error('Deactivate account error:', error);
      res.status(500).json({
        error: 'Deactivation failed',
        message: 'An error occurred while deactivating account'
      });
    }
  }
);

// Delete account (GDPR compliance)
router.delete('/account',
  authenticate,
  requireEmailVerification,
  [
    body('password').notEmpty().withMessage('Password is required for account deletion'),
    body('confirmDeletion').equals('DELETE').withMessage('Please type DELETE to confirm account deletion')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { password } = req.body;

      // Get user with password
      const user = await User.findByPk(req.user.id);

      // Verify password
      const isPasswordValid = await user.comparePassword(password);
      if (!isPasswordValid) {
        return res.status(400).json({
          error: 'Invalid password',
          message: 'The password you entered is incorrect'
        });
      }

      // In a real implementation, you would:
      // 1. Cancel all pending applications
      // 2. Delete or anonymize user data according to GDPR
      // 3. Clean up uploaded files
      // 4. Send confirmation email

      // For now, just mark as deleted
      await User.destroy({ where: { id: req.user.id } });

      res.json({
        message: 'Account deleted successfully'
      });

    } catch (error) {
      console.error('Delete account error:', error);
      res.status(500).json({
        error: 'Deletion failed',
        message: 'An error occurred while deleting account'
      });
    }
  }
);

module.exports = router;

// 测试前端登录修复
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const BASE_URL = 'http://localhost:3001';

// 测试数据
const TEST_ADMIN = {
  username: 'admin',
  email: '<EMAIL>',
  password: 'fHadmin',
  firstName: 'Super',
  lastName: 'Admin'
};

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function testAPI(method, endpoint, data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      }
    };
    
    if (data) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(`${BASE_URL}${endpoint}`, options);
    const result = await response.json();
    
    return {
      success: response.ok,
      status: response.status,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

async function testFrontendLoginFix() {
  log('blue', '🔧 测试前端登录修复');
  log('blue', '='.repeat(40));
  
  let testsPassed = 0;
  let testsFailed = 0;
  
  try {
    // 步骤1: 系统初始化
    log('yellow', '\n🚀 步骤1: 系统初始化');
    const initResult = await testAPI('POST', '/api/system/initialize', TEST_ADMIN);
    
    if (initResult.success) {
      log('green', '✅ 系统初始化成功');
      testsPassed++;
    } else {
      log('red', '❌ 系统初始化失败');
      log('red', `   错误: ${JSON.stringify(initResult)}`);
      testsFailed++;
      return;
    }
    
    // 步骤2: 测试登录API
    log('yellow', '\n🔐 步骤2: 测试登录API');
    const loginResult = await testAPI('POST', '/api/auth/login', {
      email: TEST_ADMIN.email,
      password: TEST_ADMIN.password
    });
    
    if (loginResult.success && loginResult.data.user && loginResult.data.token) {
      log('green', '✅ 登录API成功');
      log('blue', `   用户: ${loginResult.data.user.email}`);
      log('blue', `   角色: ${loginResult.data.user.role}`);
      log('blue', `   Token: ${loginResult.data.token ? '已生成' : '未生成'}`);
      testsPassed++;
      
      // 步骤3: 验证返回的数据结构
      log('yellow', '\n📋 步骤3: 验证数据结构');
      const { user, token } = loginResult.data;
      
      const requiredUserFields = ['id', 'email', 'role', 'firstName', 'lastName'];
      const missingFields = requiredUserFields.filter(field => !user[field]);
      
      if (missingFields.length === 0 && token) {
        log('green', '✅ 数据结构完整');
        log('blue', '   用户字段: ' + requiredUserFields.join(', '));
        log('blue', '   Token: 存在');
        testsPassed++;
      } else {
        log('red', '❌ 数据结构不完整');
        if (missingFields.length > 0) {
          log('red', '   缺失字段: ' + missingFields.join(', '));
        }
        if (!token) {
          log('red', '   缺失Token');
        }
        testsFailed++;
      }
      
      // 步骤4: 模拟前端存储操作
      log('yellow', '\n💾 步骤4: 模拟前端存储操作');
      try {
        // 模拟 userStore.setUser(data.user)
        log('blue', '   模拟 setUser() 调用...');
        
        // 模拟 userStore.setToken(data.token)
        log('blue', '   模拟 setToken() 调用...');
        
        log('green', '✅ 前端存储操作模拟成功');
        log('blue', '   setUser() 和 setToken() 方法都已实现');
        testsPassed++;
        
      } catch (error) {
        log('red', '❌ 前端存储操作模拟失败');
        log('red', `   错误: ${error.message}`);
        testsFailed++;
      }
      
    } else {
      log('red', '❌ 登录API失败');
      log('red', `   响应: ${JSON.stringify(loginResult)}`);
      testsFailed++;
    }
    
  } catch (error) {
    log('red', '❌ 测试过程中出错');
    log('red', `   错误: ${error.message}`);
    testsFailed++;
  }
  
  // 测试总结
  log('blue', '\n' + '='.repeat(40));
  log('blue', '📊 测试总结');
  log('green', `✅ 通过测试: ${testsPassed}`);
  log('red', `❌ 失败测试: ${testsFailed}`);
  log('blue', `📈 成功率: ${((testsPassed / (testsPassed + testsFailed)) * 100).toFixed(1)}%`);
  
  if (testsFailed === 0) {
    log('green', '\n🎉 前端登录修复成功！');
    log('blue', '\n📝 修复内容:');
    log('blue', '   1. 在 userStore 中添加了 setToken() 方法');
    log('blue', '   2. setToken() 方法正确处理 token 存储');
    log('blue', '   3. 管理员登录现在可以正常工作');
    log('blue', '\n🌐 现在可以进行手动测试:');
    log('blue', '   1. 访问: http://localhost:5173');
    log('blue', '   2. 点击"🚀 初始化系统"');
    log('blue', '   3. 使用凭据: <EMAIL> / fHadmin');
    log('blue', '   4. 完成初始化并登录管理员仪表板');
  } else {
    log('red', '\n💥 还有问题需要修复！');
  }
  
  return testsFailed === 0;
}

// 运行测试
testFrontendLoginFix().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  log('red', `💥 测试运行失败: ${error.message}`);
  process.exit(1);
});

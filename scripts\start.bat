@echo off
setlocal enabledelayedexpansion

echo 🚀 Starting Quantix Platform...

REM Check if .env file exists
if not exist "apiserver\.env" (
    echo ⚠️  Creating .env file from template...
    copy "apiserver\.env.example" "apiserver\.env"
    echo 📝 Please edit apiserver\.env with your configuration before continuing.
    echo    Required settings:
    echo    - DB_HOST, DB_USER, DB_PASSWORD, DB_NAME (MySQL)
    echo    - JWT_SECRET
    echo    - EMAIL_USER and EMAIL_PASS
    echo    - OPENAI_API_KEY
    pause
    exit /b 1
)

REM Check if frontend .env file exists
if not exist "web\.env" (
    echo ⚠️  Creating frontend .env file...
    echo VITE_API_BASE_URL=http://localhost:3001/api > web\.env
)

REM Check if node_modules exists
if not exist "web\node_modules" (
    echo 📦 Installing frontend dependencies...
    cd web
    npm install
    cd ..
)

REM Check if server node_modules exists
if not exist "apiserver\node_modules" (
    echo 📦 Installing backend dependencies...
    cd apiserver
    npm install
    cd ..
)

REM Create uploads directory
if not exist "apiserver\uploads" (
    mkdir "apiserver\uploads"
)

echo 🗄️  Setting up MySQL database...
node apiserver\scripts\setup-database.js
if %errorlevel% neq 0 (
    echo ❌ Database setup failed. Please check your MySQL configuration.
    echo    Make sure MySQL is running and credentials in apiserver\.env are correct.
    pause
    exit /b 1
)

echo 🔧 Starting backend server...
start "Quantix Backend" cmd /k "cd apiserver && npm start"

REM Wait a moment for backend to start
timeout /t 5 /nobreak > nul

echo 🎨 Starting frontend server...
start "Quantix Frontend" cmd /k "cd web && npm run dev"

echo.
echo 🎉 Quantix Platform is starting!
echo.
echo 📊 Frontend: http://localhost:5173
echo 🔧 Backend API: http://localhost:3001/api
echo 📚 API Health: http://localhost:3001/api/health
echo.
echo Press any key to open the application in your browser...
pause > nul

REM Open browser
start http://localhost:5173

echo.
echo ✅ Application opened in browser
echo.
echo To stop the services, close the backend and frontend terminal windows.
pause

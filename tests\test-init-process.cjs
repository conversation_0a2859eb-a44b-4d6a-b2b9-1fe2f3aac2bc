const { User } = require('./server/models');
const bcrypt = require('bcryptjs');

async function testInitProcess() {
  console.log('🧪 测试系统初始化过程');
  console.log('='.repeat(40));
  
  const testData = {
    username: 'admin',
    email: '<EMAIL>',
    password: 'fHadmin',
    firstName: 'Super',
    lastName: 'Admin'
  };
  
  try {
    // 确保用户不存在
    await User.destroy({ where: { email: testData.email }, force: true });
    console.log('✅ 清理完成');
    
    console.log('\n1. 测试原始密码');
    console.log('原始密码:', testData.password);
    
    console.log('\n2. 模拟系统初始化API的创建过程');
    
    // 这是系统初始化API中的代码
    const adminUser = await User.create({
      email: testData.email,
      password: testData.password, // 让模型处理密码哈希
      isEmailVerified: true,
      firstName: testData.firstName || 'Super',
      lastName: testData.lastName || 'Admin',
      organization: 'Quantix Platform',
      role: 'superadmin',
      subscriptionPlan: 'premium',
      language: 'zh'
    });
    
    console.log('✅ 用户创建成功');
    console.log('用户ID:', adminUser.id);
    console.log('用户邮箱:', adminUser.email);
    console.log('用户角色:', adminUser.role);
    
    console.log('\n3. 检查存储的密码哈希');
    const storedUser = await User.findOne({ where: { email: testData.email } });
    console.log('存储的哈希长度:', storedUser.password.length);
    console.log('存储的哈希:', storedUser.password);
    
    console.log('\n4. 测试密码验证');
    const isValid = await storedUser.comparePassword(testData.password);
    console.log('comparePassword结果:', isValid);
    
    const directValid = await bcrypt.compare(testData.password, storedUser.password);
    console.log('直接bcrypt.compare结果:', directValid);
    
    console.log('\n5. 测试登录API逻辑');
    // 模拟登录API的逻辑
    const loginUser = await User.findOne({ where: { email: testData.email } });
    if (loginUser) {
      const loginValid = await loginUser.comparePassword(testData.password);
      console.log('登录验证结果:', loginValid);
      
      if (loginValid) {
        console.log('✅ 登录成功！');
      } else {
        console.log('❌ 登录失败！');
        
        // 尝试调试
        console.log('\n调试信息:');
        console.log('输入密码:', testData.password);
        console.log('输入密码长度:', testData.password.length);
        console.log('存储哈希:', loginUser.password);
        console.log('存储哈希长度:', loginUser.password.length);
        
        // 尝试手动验证
        const manualTest = await bcrypt.compare(testData.password, loginUser.password);
        console.log('手动bcrypt验证:', manualTest);
      }
    }
    
    console.log('\n6. 测试其他密码');
    const testPasswords = ['admin', 'password', '123456', 'fHadmin'];
    for (const pwd of testPasswords) {
      const result = await storedUser.comparePassword(pwd);
      console.log(`密码 "${pwd}":`, result);
    }
    
  } catch (error) {
    console.error('❌ 测试过程中出错:', error);
  }
  
  process.exit();
}

testInitProcess();

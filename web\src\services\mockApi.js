// Mock API for frontend debugging
// This file provides mock implementations of all API calls for development and testing

// Mock data
const mockUsers = [
  {
    id: 1,
    email: "<EMAIL>",
    firstName: "Admin",
    lastName: "User",
    role: "admin",
    isActive: true,
    isEmailVerified: true,
    createdAt: "2023-01-01T00:00:00Z",
  },
  {
    id: 2,
    email: "<EMAIL>",
    firstName: "Test",
    lastName: "User",
    role: "user",
    isActive: true,
    isEmailVerified: true,
    createdAt: "2023-01-02T00:00:00Z",
  },
];

const mockApplications = [
  {
    id: 1,
    applicationId: "APP-2023-001",
    contactPhone: "************",
    analysisType: "protein_complex",
    status: "completed",
    progressPercentage: 100,
    currentStep: "Analysis Complete",
    files: [{ originalName: "sample.fasta", size: 1024 }],
    createdAt: "2023-01-01T00:00:00Z",
    previewReportFile: {
      originalName: "preview_report.pdf",
      size: 2048,
      uploadedAt: "2023-01-02T00:00:00Z",
    },
    fullReportFile: {
      originalName: "full_report.pdf",
      size: 5120,
      uploadedAt: "2023-01-02T00:00:00Z",
    },
    reportPrice: 99.99,
    reportPaymentStatus: "unpaid",
  },
];

// Mock response helper
const mockResponse = (data, delay = 500) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ data });
    }, delay);
  });
};

// Mock error helper
const mockError = (message, status = 400, delay = 500) => {
  return new Promise((_, reject) => {
    setTimeout(() => {
      reject({
        response: {
          status,
          data: { message },
        },
      });
    }, delay);
  });
};

// Mock Auth API
export const mockAuthAPI = {
  register: (data) => {
    console.log("Mock: Register user", data);
    return mockResponse({
      message: "Registration successful",
      user: { ...data, id: Date.now(), role: "user" },
    });
  },

  verifyEmail: (data) => {
    console.log("Mock: Verify email", data);
    return mockResponse({
      message: "Email verified successfully",
      token: "mock-token-" + Date.now(),
      user: mockUsers[1],
    });
  },

  login: (data) => {
    console.log("Mock: Login user", data);
    const user = mockUsers.find((u) => u.email === data.email);
    if (user) {
      return mockResponse({
        message: "Login successful",
        token: "mock-token-" + Date.now(),
        user,
      });
    }
    return mockError("Invalid credentials", 401);
  },

  logout: () => {
    console.log("Mock: Logout user");
    return mockResponse({ message: "Logout successful" });
  },

  getCurrentUser: () => {
    console.log("Mock: Get current user");
    return mockResponse({ user: mockUsers[0] });
  },
};

// Mock System API
export const mockSystemAPI = {
  checkInitStatus: () => {
    console.log("Mock: Check system init status");
    return mockResponse({
      isInitialized: true,
      adminCount: 1,
      message: "System is initialized",
    });
  },

  initialize: (data) => {
    console.log("Mock: Initialize system", data);
    return mockResponse({
      message: "System initialized successfully",
      user: { ...data, id: 1, role: "superadmin" },
    });
  },
};

// Mock Application API
export const mockApplicationAPI = {
  submitApplication: (data) => {
    console.log("Mock: Submit application", data);
    return mockResponse({
      message: "Application submitted successfully",
      application: {
        id: Date.now(),
        applicationId: `APP-${Date.now()}`,
        ...data,
        status: "pending",
      },
    });
  },

  getApplications: (params) => {
    console.log("Mock: Get applications", params);
    return mockResponse({
      applications: mockApplications,
      pagination: { page: 1, totalPages: 1, total: mockApplications.length },
    });
  },

  getApplication: (id) => {
    console.log("Mock: Get application", id);
    const app = mockApplications.find((a) => a.id == id);
    return app ? mockResponse({ application: app }) : mockError("Application not found", 404);
  },

  cancelApplication: (id) => {
    console.log("Mock: Cancel application", id);
    return mockResponse({ message: "Application cancelled successfully" });
  },

  downloadReport: (id, reportType) => {
    console.log("Mock: Download report", id, reportType);
    // Create a mock blob for download
    const mockPdfContent = "Mock PDF content for " + reportType + " report";
    const blob = new Blob([mockPdfContent], { type: "application/pdf" });
    return mockResponse(blob, 1000);
  },
};

// Mock Upload API
export const mockUploadAPI = {
  uploadFiles: (formData) => {
    console.log("Mock: Upload files", formData);
    const files = [];
    for (let [, file] of formData.entries()) {
      if (file instanceof File) {
        files.push({
          filename: `mock-${Date.now()}-${file.name}`,
          originalName: file.name,
          size: file.size,
          uploadedAt: new Date().toISOString(),
        });
      }
    }
    return mockResponse({ files });
  },

  deleteFile: (filename) => {
    console.log("Mock: Delete file", filename);
    return mockResponse({ message: "File deleted successfully" });
  },
};

// Mock Admin API
export const mockAdminAPI = {
  getDashboardStats: () => {
    console.log("Mock: Get dashboard stats");
    return mockResponse({
      totalUsers: 150,
      totalApplications: 75,
      pendingApplications: 12,
      completedApplications: 63,
      systemHealth: {
        status: "healthy",
        uptime: 86400,
        memoryUsage: 65.5,
        cpuUsage: 23.8,
      },
    });
  },

  getUsers: (params) => {
    console.log("Mock: Get users", params);
    return mockResponse({
      users: mockUsers,
      pagination: { page: 1, totalPages: 1, total: mockUsers.length },
    });
  },

  createUser: (data) => {
    console.log("Mock: Create user", data);
    return mockResponse({
      message: "User created successfully",
      user: { ...data, id: Date.now() },
    });
  },

  updateUser: (id, data) => {
    console.log("Mock: Update user", id, data);
    return mockResponse({ message: "User updated successfully" });
  },

  deleteUser: (id) => {
    console.log("Mock: Delete user", id);
    return mockResponse({ message: "User deleted successfully" });
  },

  toggleUserStatus: (id, action) => {
    console.log("Mock: Toggle user status", id, action);
    return mockResponse({ message: `User ${action}d successfully` });
  },

  promoteUser: (id, data) => {
    console.log("Mock: Promote user", id, data);
    return mockResponse({ message: "User promoted successfully" });
  },

  resetUserPassword: (id, data) => {
    console.log("Mock: Reset user password", id, data);
    return mockResponse({ message: "Password reset successfully" });
  },
};

// Mock Payment API
export const mockPaymentAPI = {
  createPayment: (data) => {
    console.log("Mock: Create payment", data);
    return mockResponse({
      success: true,
      payment: {
        paymentId: `PAY-${Date.now()}`,
        amount: data.amount,
        paymentMethod: data.paymentMethod,
        status: "pending",
        qrCodeContent: `mock-qr-${Date.now()}`,
        expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString(),
      },
    });
  },

  getPaymentStatus: (paymentId) => {
    console.log("Mock: Get payment status", paymentId);
    return mockResponse({
      success: true,
      payment: {
        paymentId,
        status: "pending",
        amount: 99.99,
        paymentMethod: "wechat",
      },
    });
  },

  simulatePayment: (paymentId) => {
    console.log("Mock: Simulate payment", paymentId);
    return mockResponse({
      success: true,
      payment: {
        paymentId,
        status: "paid",
        paidAt: new Date().toISOString(),
      },
    });
  },

  cancelPayment: (paymentId) => {
    console.log("Mock: Cancel payment", paymentId);
    return mockResponse({
      success: true,
      message: "Payment cancelled successfully",
    });
  },
};

// Export all mock APIs
export const mockAPI = {
  auth: mockAuthAPI,
  system: mockSystemAPI,
  application: mockApplicationAPI,
  upload: mockUploadAPI,
  admin: mockAdminAPI,
  payment: mockPaymentAPI,
};

export default mockAPI;

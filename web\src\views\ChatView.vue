<template>
  <div class="chat-container">
    <div class="chat-header">
      <h2>{{ $t('qaTitle') }}</h2>
      <button @click="goBack" class="btn-back">{{ $t('back') }}</button>
    </div>
    <div class="chat-messages" ref="chatMessages">
      <div v-for="(message, index) in messages" :key="index" :class="['message', message.sender]">
        <div class="message-content">{{ message.text }}</div>
      </div>
    </div>
    <div class="chat-input">
      <input
        type="text"
        v-model="newMessage"
        @keyup.enter="sendMessage"
        :placeholder="$t('enterYourQuestion')"
      />
      <button @click="sendMessage" class="btn-send">{{ $t('send') }}</button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, nextTick, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'

export default defineComponent({
  name: 'ChatView',
  setup() {
    const router = useRouter()
    const { t } = useI18n() // Use useI18n to get the translation function
    const messages = ref<{ sender: string; text: string }[]>([
      { sender: 'system', text: t('helloAiAssistant') }, // Use t() instead of i18n.global.t()
    ])
    const newMessage = ref('')
    const chatMessages = ref<HTMLElement | null>(null)

    const scrollToBottom = () => {
      nextTick(() => {
        if (chatMessages.value) {
          chatMessages.value.scrollTop = chatMessages.value.scrollHeight
        }
      })
    }

    const sendMessage = async () => {
      if (newMessage.value.trim() === '') return

      messages.value.push({ sender: 'user', text: newMessage.value })
      const userQuestion = newMessage.value
      newMessage.value = ''
      scrollToBottom()

      // 模拟调用大语言模型接口
      try {
        // 实际应用中，这里会调用后端接口，例如：
        // const response = await fetch('/api/protein-qa', {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify({ question: userQuestion }),
        // });
        // const data = await response.json();
        // messages.value.push({ sender: 'system', text: data.answer });

        // 模拟异步响应
        setTimeout(() => {
          const systemAnswer = t('complexQuestionResponse', { question: userQuestion }) // Use t()
          messages.value.push({ sender: 'system', text: systemAnswer })
          scrollToBottom()
        }, 1000)
      } catch (error) {
        console.error('调用AI接口失败:', error)
        messages.value.push({ sender: 'system', text: t('aiAssistantError') }) // Use t()
        scrollToBottom()
      }
    }

    const goBack = () => {
      router.back()
    }

    onMounted(() => {
      scrollToBottom()
    })

    return {
      messages,
      newMessage,
      sendMessage,
      goBack,
      chatMessages,
    }
  },
})
</script>

<style scoped>
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-width: 800px;
  margin: 0 auto;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background-color: #f8f8f8;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #007bff;
  color: white;
  border-bottom: 1px solid #0056b3;
}

.chat-header h2 {
  margin: 0;
  font-size: 22px;
}

.btn-back {
  background-color: #0056b3;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s ease;
}

.btn-back:hover {
  background-color: #004085;
}

.chat-messages {
  flex-grow: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #ffffff;
}

.message {
  margin-bottom: 15px;
  display: flex;
}

.message.user {
  justify-content: flex-end;
}

.message.system {
  justify-content: flex-start;
}

.message-content {
  max-width: 70%;
  padding: 10px 15px;
  border-radius: 18px;
  line-height: 1.4;
  word-wrap: break-word;
}

.message.user .message-content {
  background-color: #e1ffc7;
  color: #333;
  border-bottom-right-radius: 2px;
}

.message.system .message-content {
  background-color: #e0e0e0;
  color: #333;
  border-bottom-left-radius: 2px;
}

.chat-input {
  display: flex;
  padding: 15px 20px;
  border-top: 1px solid #e0e0e0;
  background-color: #f0f0f0;
}

.chat-input input {
  flex-grow: 1;
  padding: 12px 15px;
  border: 1px solid #ccc;
  border-radius: 25px;
  font-size: 16px;
  margin-right: 10px;
  outline: none;
}

.chat-input input:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.btn-send {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s ease;
}

.btn-send:hover {
  background-color: #218838;
}
</style>

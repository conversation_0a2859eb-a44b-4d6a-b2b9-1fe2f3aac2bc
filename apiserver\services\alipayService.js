const AlipaySdk = require('alipay-sdk').default;
const QRCode = require('qrcode');

class AlipayService {
  constructor() {
    // 支付宝配置（这些应该从环境变量中获取）
    this.config = {
      appId: process.env.ALIPAY_APPID || 'your_alipay_appid',
      privateKey: process.env.ALIPAY_PRIVATE_KEY || '', // 应用私钥
      alipayPublicKey: process.env.ALIPAY_PUBLIC_KEY || '', // 支付宝公钥
      gateway:
        process.env.ALIPAY_GATEWAY || 'https://openapi.alipay.com/gateway.do', // 支付宝网关
      notifyUrl:
        process.env.ALIPAY_NOTIFY_URL ||
        'http://localhost:3001/api/payments/callback/alipay',
      returnUrl:
        process.env.ALIPAY_RETURN_URL ||
        'http://localhost:5173/payment/success'
    };

    // 初始化支付宝SDK（仅在有配置时）
    this.alipaySdk = null;
    if (
      this.config.privateKey &&
      this.config.appId &&
      this.config.alipayPublicKey
    ) {
      try {
        this.alipaySdk = new AlipaySdk({
          appId: this.config.appId,
          privateKey: this.config.privateKey,
          alipayPublicKey: this.config.alipayPublicKey,
          gateway: this.config.gateway,
          timeout: 5000, // 网关超时时间
          camelCase: true // 是否把网关返回的下划线key转换为驼峰写法
        });
      } catch (error) {
        console.warn('支付宝支付初始化失败，将使用模拟模式:', error.message);
      }
    } else {
      console.warn('支付宝支付配置不完整，将使用模拟模式');
    }
  }

  /**
   * 创建扫码支付订单
   * @param {Object} orderData - 订单数据
   * @returns {Promise<Object>} 支付订单信息
   */
  async createQRPayOrder(orderData) {
    try {
      const { paymentId, amount, description, userId } = orderData;

      // 如果没有配置支付宝，使用模拟模式
      if (!this.alipaySdk) {
        console.log('使用支付宝支付模拟模式');
        return {
          success: true,
          qrCodeContent: `https://qr.alipay.com/${paymentId}?amount=${amount}`,
          orderData: {
            code: '10000',
            msg: 'Success',
            qr_code: `https://qr.alipay.com/${paymentId}?amount=${amount}`,
            out_trade_no: paymentId
          }
        };
      }

      const bizContent = {
        outTradeNo: paymentId, // 商户订单号
        totalAmount: amount.toFixed(2), // 订单总金额，单位为元
        subject: description || '蛋白质分析报告', // 订单标题
        body: `用户${userId}购买蛋白质分析报告`, // 订单描述
        timeoutExpress: '30m', // 订单超时时间
        productCode: 'FACE_TO_FACE_PAYMENT', // 产品码，扫码支付为FACE_TO_FACE_PAYMENT
        storeId: 'protein_analysis_platform', // 商户门店编号
        operatorId: 'system' // 商户操作员编号
      };

      // 调用支付宝API创建订单
      const result = await this.alipaySdk.exec('alipay.trade.precreate', {
        bizContent: bizContent,
        notifyUrl: this.config.notifyUrl
      });

      if (result.code === '10000' && result.qrCode) {
        return {
          success: true,
          qrCodeContent: result.qrCode,
          orderData: result
        };
      } else {
        throw new Error(`Alipay API error: ${result.msg || result.subMsg}`);
      }
    } catch (error) {
      console.error('Alipay create order error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 生成支付二维码图片
   * @param {string} qrCode - 支付宝二维码内容
   * @returns {Promise<string>} Base64编码的二维码图片
   */
  async generateQRCode(qrCode) {
    try {
      const qrCodeDataURL = await QRCode.toDataURL(qrCode, {
        width: 256,
        margin: 2,
        color: {
          dark: '#1677FF', // 支付宝蓝色
          light: '#FFFFFF'
        }
      });
      return qrCodeDataURL;
    } catch (error) {
      console.error('Generate QR code error:', error);
      throw error;
    }
  }

  /**
   * 查询订单状态
   * @param {string} outTradeNo - 商户订单号
   * @returns {Promise<Object>} 订单状态信息
   */
  async queryOrder(outTradeNo) {
    try {
      const result = await this.alipaySdk.exec('alipay.trade.query', {
        bizContent: {
          outTradeNo: outTradeNo
        }
      });

      return {
        success: result.code === '10000',
        data: result
      };
    } catch (error) {
      console.error('Alipay query order error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 取消订单
   * @param {string} outTradeNo - 商户订单号
   * @returns {Promise<Object>} 取消结果
   */
  async cancelOrder(outTradeNo) {
    try {
      const result = await this.alipaySdk.exec('alipay.trade.cancel', {
        bizContent: {
          outTradeNo: outTradeNo
        }
      });

      return {
        success: result.code === '10000',
        data: result
      };
    } catch (error) {
      console.error('Alipay cancel order error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 关闭订单
   * @param {string} outTradeNo - 商户订单号
   * @returns {Promise<Object>} 关闭结果
   */
  async closeOrder(outTradeNo) {
    try {
      const result = await this.alipaySdk.exec('alipay.trade.close', {
        bizContent: {
          outTradeNo: outTradeNo
        }
      });

      return {
        success: result.code === '10000',
        data: result
      };
    } catch (error) {
      console.error('Alipay close order error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 验证支付回调签名
   * @param {Object} postData - 回调数据
   * @returns {boolean} 验证结果
   */
  verifySignature(postData) {
    try {
      // 使用支付宝SDK验证签名
      const result = this.alipaySdk.checkNotifySign(postData);
      return result;
    } catch (error) {
      console.error('Alipay verify signature error:', error);
      return false;
    }
  }

  /**
   * 创建退款订单
   * @param {Object} refundData - 退款数据
   * @returns {Promise<Object>} 退款结果
   */
  async createRefund(refundData) {
    try {
      const { outTradeNo, refundAmount, reason } = refundData;

      const bizContent = {
        outTradeNo: outTradeNo,
        refundAmount: refundAmount.toFixed(2),
        refundReason: reason || '用户申请退款',
        outRequestNo: `REFUND_${outTradeNo}_${Date.now()}` // 退款请求号
      };

      const result = await this.alipaySdk.exec('alipay.trade.refund', {
        bizContent: bizContent
      });

      return {
        success: result.code === '10000',
        data: result
      };
    } catch (error) {
      console.error('Alipay create refund error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 查询退款状态
   * @param {string} outTradeNo - 商户订单号
   * @param {string} outRequestNo - 退款请求号
   * @returns {Promise<Object>} 退款状态信息
   */
  async queryRefund(outTradeNo, outRequestNo) {
    try {
      const result = await this.alipaySdk.exec(
        'alipay.trade.fastpay.refund.query',
        {
          bizContent: {
            outTradeNo: outTradeNo,
            outRequestNo: outRequestNo
          }
        }
      );

      return {
        success: result.code === '10000',
        data: result
      };
    } catch (error) {
      console.error('Alipay query refund error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 解析回调通知数据
   * @param {Object} postData - POST回调数据
   * @returns {Object} 解析后的数据
   */
  parseNotifyData(postData) {
    try {
      // 支付宝回调数据已经是解析好的格式
      return {
        outTradeNo: postData.out_trade_no,
        tradeNo: postData.trade_no,
        tradeStatus: postData.trade_status,
        totalAmount: parseFloat(postData.total_amount),
        receiptAmount: parseFloat(
          postData.receipt_amount || postData.total_amount
        ),
        buyerPayAmount: parseFloat(
          postData.buyer_pay_amount || postData.total_amount
        ),
        gmtPayment: postData.gmt_payment,
        subject: postData.subject,
        body: postData.body,
        sellerId: postData.seller_id,
        buyerId: postData.buyer_id,
        buyerLogonId: postData.buyer_logon_id
      };
    } catch (error) {
      console.error('Alipay parse notify data error:', error);
      throw error;
    }
  }
}

module.exports = AlipayService;

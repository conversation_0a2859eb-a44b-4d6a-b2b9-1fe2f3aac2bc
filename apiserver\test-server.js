const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:5173',
  credentials: true
}));
app.use(express.json());

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Quantix Backend API is running',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Test endpoints
app.post('/api/auth/register', (req, res) => {
  res.json({
    message: 'User registered successfully. Please check your email for verification code.',
    userId: 'test-user-id',
    email: req.body.email
  });
});

app.post('/api/auth/verify-email', (req, res) => {
  res.json({
    message: 'Email verified successfully',
    token: 'test-jwt-token',
    user: {
      id: 'test-user-id',
      email: req.body.email,
      isEmailVerified: true
    }
  });
});

app.post('/api/auth/login', (req, res) => {
  res.json({
    message: 'Login successful',
    token: 'test-jwt-token',
    user: {
      id: 'test-user-id',
      email: req.body.email,
      isEmailVerified: true,
      firstName: 'Test',
      lastName: 'User'
    }
  });
});

app.get('/api/auth/me', (req, res) => {
  res.json({
    user: {
      id: 'test-user-id',
      email: '<EMAIL>',
      isEmailVerified: true,
      firstName: 'Test',
      lastName: 'User'
    }
  });
});

app.post('/api/chat/public/message', (req, res) => {
  const { message, language } = req.body;
  res.json({
    success: true,
    data: {
      question: message,
      answer: `这是对"${message}"的模拟回答。在真实环境中，这里会调用AI服务来生成专业的蛋白质分析回答。`,
      language: language || 'zh',
      source: 'test',
      confidence: 0.8,
      timestamp: new Date().toISOString()
    }
  });
});

app.get('/api/chat/suggestions', (req, res) => {
  const language = req.query.language || 'zh';
  const suggestions = language === 'zh' ? [
    '什么是蛋白质复合物？',
    '如何分析蛋白质相互作用？',
    'AlphaFold在蛋白质结构预测中的应用是什么？'
  ] : [
    'What are protein complexes?',
    'How to analyze protein interactions?',
    'What is the application of AlphaFold in protein structure prediction?'
  ];

  res.json({
    suggestions,
    language
  });
});

// Error handling
app.use((err, req, res, _next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Internal Server Error',
    message: 'Something went wrong!'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: 'The requested resource was not found'
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Test Server running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🎯 Frontend URL: ${process.env.FRONTEND_URL || 'http://localhost:5173'}`);
});

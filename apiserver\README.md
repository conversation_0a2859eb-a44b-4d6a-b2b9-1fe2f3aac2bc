# 蛋白质复合物分析平台 - 后端服务

这是一个基于Node.js和Express.js构建的蛋白质复合物分析平台后端服务，为前端Vue.js应用提供完整的API支持。

## 功能特性

- 🔐 **用户认证系统**: JWT认证、邮箱验证、密码重置
- 📁 **文件管理**: 基因数据文件上传、验证、存储
- 🧬 **分析申请**: 蛋白质复合物分析申请提交和管理
- 📊 **报告系统**: 分析报告生成、预览、付费下载
- 🤖 **AI问答**: 集成大语言模型的智能问答服务
- 🌍 **多语言支持**: 支持中文、英文等多种语言
- 💳 **订阅管理**: 用户订阅计划和权限管理

## 技术栈

- **运行时**: Node.js 18+
- **框架**: Express.js
- **数据库**: MongoDB + Mongoose ODM
- **认证**: JWT + bcryptjs
- **文件处理**: Multer
- **邮件服务**: Nodemailer
- **AI服务**: OpenAI API
- **测试**: Jest + Supertest
- **文档**: 内置API文档

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 环境配置

复制环境配置文件并修改配置：

```bash
cp server/.env.example server/.env
```

编辑 `server/.env` 文件，配置以下关键参数：

```env
# 数据库连接
MONGODB_URI=mongodb://localhost:27017/quantix

# JWT密钥（生产环境请使用强密钥）
JWT_SECRET=your-super-secret-jwt-key

# 邮件服务配置
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# OpenAI API密钥（用于AI问答）
OPENAI_API_KEY=your-openai-api-key
```

### 3. 启动MongoDB

确保MongoDB服务正在运行：

```bash
# macOS (使用Homebrew)
brew services start mongodb-community

# Ubuntu/Debian
sudo systemctl start mongod

# Windows
net start MongoDB
```

### 4. 启动开发服务器

```bash
npm run server:dev
```

服务器将在 http://localhost:3001 启动。

### 5. 启动前端开发服务器

在另一个终端窗口中：

```bash
npm run dev
```

前端将在 http://localhost:5173 启动。

## API 端点

### 认证相关
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/verify-email` - 邮箱验证
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/me` - 获取当前用户信息
- `POST /api/auth/logout` - 用户登出

### 用户管理
- `PATCH /api/users/profile` - 更新用户资料
- `PATCH /api/users/preferences` - 更新用户偏好
- `PATCH /api/users/password` - 修改密码
- `GET /api/users/stats` - 获取用户统计信息

### 文件管理
- `POST /api/upload/files` - 上传文件
- `GET /api/upload/files` - 获取文件列表
- `GET /api/upload/files/:filename` - 获取文件信息
- `GET /api/upload/files/:filename/download` - 下载文件
- `DELETE /api/upload/files/:filename` - 删除文件

### 分析申请
- `POST /api/applications` - 提交分析申请
- `GET /api/applications` - 获取申请列表
- `GET /api/applications/:id` - 获取申请详情
- `PATCH /api/applications/:id` - 更新申请
- `POST /api/applications/:id/cancel` - 取消申请

### 报告管理
- `GET /api/reports/application/:applicationId` - 根据申请ID获取报告
- `GET /api/reports/:reportId` - 根据报告ID获取报告
- `GET /api/reports/:reportId/download` - 下载报告
- `GET /api/reports` - 获取报告列表

### AI问答
- `POST /api/chat/message` - 发送消息（需认证）
- `POST /api/chat/public/message` - 公开消息（限制更严格）
- `GET /api/chat/suggestions` - 获取建议问题
- `GET /api/chat/health` - AI服务健康检查

## 数据模型

### User（用户）
- 基本信息：邮箱、密码、验证状态
- 个人资料：姓名、组织、电话、国家
- 偏好设置：语言、通知设置
- 订阅信息：计划类型、到期时间

### Application（分析申请）
- 申请信息：申请ID、用户、联系方式
- 文件信息：上传的基因数据文件
- 分析参数：算法选择、置信度等
- 状态跟踪：待处理、处理中、已完成等
- 结果数据：分析结果、报告链接

### Report（分析报告）
- 报告基本信息：标题、摘要、状态
- 分析结果：蛋白质复合物、相互作用等
- 可视化数据：图表、结构图等
- 访问控制：公开性、付费设置

## 开发指南

### 项目结构

```
server/
├── config/          # 配置文件
├── middleware/      # 中间件
├── models/          # 数据模型
├── routes/          # 路由处理
├── services/        # 业务服务
├── utils/           # 工具函数
├── uploads/         # 文件上传目录
├── tests/           # 测试文件
└── index.js         # 入口文件
```

### 运行测试

```bash
npm run server:test
```

### 代码规范

项目使用ESLint和Prettier进行代码格式化：

```bash
npm run lint
npm run format
```

## 部署

### 生产环境配置

1. 设置环境变量：
```bash
export NODE_ENV=production
export MONGODB_URI=mongodb://your-production-db
export JWT_SECRET=your-production-jwt-secret
```

2. 构建和启动：
```bash
npm run build
npm run server:start
```

### Docker部署

```bash
# 构建镜像
docker build -t quantix-backend .

# 运行容器
docker run -p 3001:3001 -e NODE_ENV=production quantix-backend
```

## 监控和日志

- 使用Morgan进行HTTP请求日志记录
- 错误日志自动记录到控制台
- 生产环境建议使用专业的日志服务

## 安全考虑

- JWT令牌安全存储和验证
- 密码使用bcrypt加密
- 文件上传类型和大小限制
- API速率限制
- CORS配置
- Helmet安全头设置

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License

## 支持

如有问题，请创建Issue或联系开发团队。

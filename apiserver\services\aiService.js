const OpenAI = require('openai');

// Initialize OpenAI client only if API key is available
let openai = null;
if (process.env.OPENAI_API_KEY) {
  openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY
  });
} else {
  console.warn('⚠️  OPENAI_API_KEY not configured - AI chat will use fallback responses only');
}

// System prompt for protein analysis Q&A
const SYSTEM_PROMPT = `You are an expert AI assistant specializing in protein complex analysis, bioinformatics, and molecular biology. Your role is to provide accurate, helpful, and scientifically sound answers to questions related to:

1. Protein structure and function
2. Protein-protein interactions
3. Protein complex analysis
4. Bioinformatics tools and methods
5. Molecular dynamics simulations
6. Drug discovery and protein targets
7. Structural biology techniques
8. Sequence analysis and alignment
9. Phylogenetic analysis
10. Biochemical pathways and networks

Guidelines for responses:
- Provide accurate, evidence-based information
- Use appropriate scientific terminology while remaining accessible
- Include relevant examples when helpful
- Suggest additional resources or tools when appropriate
- If uncertain about specific details, acknowledge limitations
- Focus on practical applications and real-world relevance
- Support answers with current scientific understanding

Always maintain a professional, helpful tone and prioritize accuracy over speculation.`;

// Predefined responses for common questions
const PREDEFINED_RESPONSES = {
  'zh': {
    'greeting': '您好！我是蛋白质复合物分析AI助手。我可以帮助您解答关于蛋白质结构、功能、相互作用以及生物信息学分析方面的问题。请问有什么可以帮助您的吗？',
    'protein_structure': '蛋白质结构分析是理解蛋白质功能的关键。蛋白质具有四级结构：一级结构（氨基酸序列）、二级结构（α螺旋和β折叠）、三级结构（整体折叠）和四级结构（多个亚基组装）。',
    'protein_interaction': '蛋白质相互作用是细胞功能的基础。常用的研究方法包括酵母双杂交、免疫共沉淀、质谱分析和计算预测等。',
    'drug_discovery': '基于蛋白质的药物发现涉及靶点识别、结构解析、化合物筛选和优化等步骤。结构生物学在其中发挥重要作用。'
  },
  'en': {
    'greeting': 'Hello! I am your protein complex analysis AI assistant. I can help you with questions about protein structure, function, interactions, and bioinformatics analysis. How can I assist you today?',
    'protein_structure': 'Protein structure analysis is key to understanding protein function. Proteins have four levels of structure: primary (amino acid sequence), secondary (α-helices and β-sheets), tertiary (overall folding), and quaternary (multi-subunit assembly).',
    'protein_interaction': 'Protein interactions are fundamental to cellular function. Common research methods include yeast two-hybrid, co-immunoprecipitation, mass spectrometry, and computational prediction.',
    'drug_discovery': 'Protein-based drug discovery involves target identification, structure determination, compound screening, and optimization. Structural biology plays a crucial role in this process.'
  }
};

// Function to detect question category
const detectQuestionCategory = (question) => {
  const lowerQuestion = question.toLowerCase();

  if (lowerQuestion.includes('结构') || lowerQuestion.includes('structure')) {
    return 'protein_structure';
  }
  if (lowerQuestion.includes('相互作用') || lowerQuestion.includes('interaction')) {
    return 'protein_interaction';
  }
  if (lowerQuestion.includes('药物') || lowerQuestion.includes('drug')) {
    return 'drug_discovery';
  }
  if (lowerQuestion.includes('你好') || lowerQuestion.includes('hello') || lowerQuestion.includes('帮助') || lowerQuestion.includes('help')) {
    return 'greeting';
  }

  return null;
};

// Generate AI response using OpenAI
const generateAIResponse = async (question, language = 'zh', context = null) => {
  try {
    // Check for predefined responses first
    const category = detectQuestionCategory(question);
    if (category && PREDEFINED_RESPONSES[language] && PREDEFINED_RESPONSES[language][category]) {
      return {
        answer: PREDEFINED_RESPONSES[language][category],
        source: 'predefined',
        confidence: 0.9
      };
    }

    // If OpenAI is not available, use fallback response
    if (!openai) {
      const fallbackResponses = {
        'zh': '抱歉，AI服务暂时不可用。这可能是一个复杂的专业问题，建议您咨询相关领域的专家或查阅最新的科学文献。',
        'en': 'Sorry, AI service is temporarily unavailable. This might be a complex specialized question. I recommend consulting experts in the relevant field or referring to recent scientific literature.'
      };

      return {
        answer: fallbackResponses[language] || fallbackResponses['en'],
        source: 'fallback',
        confidence: 0.1,
        error: 'OpenAI API not configured'
      };
    }

    // Prepare messages for OpenAI
    const messages = [
      {
        role: 'system',
        content: SYSTEM_PROMPT + (language === 'zh' ? '\n\n请用中文回答问题。' : '\n\nPlease respond in English.')
      }
    ];

    // Add context if provided
    if (context) {
      messages.push({
        role: 'system',
        content: `Context: ${context}`
      });
    }

    messages.push({
      role: 'user',
      content: question
    });

    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || 'gpt-3.5-turbo',
      messages: messages,
      max_tokens: 1000,
      temperature: 0.7,
      top_p: 0.9,
      frequency_penalty: 0.1,
      presence_penalty: 0.1
    });

    const answer = completion.choices[0].message.content.trim();

    return {
      answer: answer,
      source: 'openai',
      confidence: 0.8,
      usage: completion.usage
    };

  } catch (error) {
    console.error('OpenAI API error:', error);

    // Fallback response
    const fallbackResponses = {
      'zh': '抱歉，我暂时无法回答您的问题。这可能是一个复杂的专业问题，建议您咨询相关领域的专家或查阅最新的科学文献。',
      'en': 'I apologize, but I cannot answer your question at the moment. This might be a complex specialized question. I recommend consulting experts in the relevant field or referring to recent scientific literature.'
    };

    return {
      answer: fallbackResponses[language] || fallbackResponses['en'],
      source: 'fallback',
      confidence: 0.1,
      error: error.message
    };
  }
};

// Generate response with rate limiting and caching
const generateResponse = async (question, options = {}) => {
  const {
    language = 'zh',
    context = null,
    userId = null,
    sessionId = null
  } = options;

  try {
    // Input validation
    if (!question || question.trim().length === 0) {
      throw new Error('Question cannot be empty');
    }

    if (question.length > 1000) {
      throw new Error('Question is too long (maximum 1000 characters)');
    }

    // Generate response
    const result = await generateAIResponse(question, language, context);

    // Log the interaction (in production, save to database)
    console.log(`AI Q&A - User: ${userId}, Session: ${sessionId}, Question: ${question.substring(0, 100)}..., Source: ${result.source}`);

    return {
      success: true,
      data: {
        question: question,
        answer: result.answer,
        language: language,
        source: result.source,
        confidence: result.confidence,
        timestamp: new Date().toISOString(),
        ...(result.usage && { usage: result.usage })
      }
    };

  } catch (error) {
    console.error('Generate response error:', error);

    return {
      success: false,
      error: {
        message: error.message,
        code: 'AI_SERVICE_ERROR'
      }
    };
  }
};

// Get suggested questions based on language
const getSuggestedQuestions = (language = 'zh') => {
  const suggestions = {
    'zh': [
      '什么是蛋白质复合物？',
      '如何分析蛋白质相互作用？',
      'AlphaFold在蛋白质结构预测中的应用是什么？',
      '蛋白质结构与功能的关系是什么？',
      '如何进行蛋白质序列比对？',
      '什么是分子对接？',
      '蛋白质折叠的机制是什么？',
      '如何识别潜在的药物靶点？'
    ],
    'en': [
      'What are protein complexes?',
      'How to analyze protein interactions?',
      'What is the application of AlphaFold in protein structure prediction?',
      'What is the relationship between protein structure and function?',
      'How to perform protein sequence alignment?',
      'What is molecular docking?',
      'What is the mechanism of protein folding?',
      'How to identify potential drug targets?'
    ]
  };

  return suggestions[language] || suggestions['en'];
};

// Validate API configuration
const validateConfiguration = () => {
  const issues = [];

  if (!process.env.OPENAI_API_KEY) {
    issues.push('OPENAI_API_KEY is not configured');
  }

  if (!process.env.OPENAI_MODEL) {
    console.warn('OPENAI_MODEL not specified, using default: gpt-3.5-turbo');
  }

  return {
    isValid: issues.length === 0,
    issues: issues
  };
};

module.exports = {
  generateResponse,
  getSuggestedQuestions,
  validateConfiguration
};

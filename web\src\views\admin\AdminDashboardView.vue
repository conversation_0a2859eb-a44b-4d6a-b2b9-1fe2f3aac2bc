<template>
  <div class="admin-dashboard">
    <div class="admin-header">
      <div class="header-left">
        <h1>{{ $t('adminDashboard') }}</h1>
        <router-link to="/" class="homepage-link">
          <span class="homepage-icon">🏠</span>
          {{ $t('backToHomepage') }}
        </router-link>
      </div>
      <div class="admin-user-info">
        <div class="language-switcher">
          <select v-model="currentLocale" @change="changeLanguage" class="language-select">
            <option value="en">English</option>
            <option value="zh">中文</option>
            <option value="es">Español</option>
            <option value="fr">Français</option>
            <option value="de">Deutsch</option>
            <option value="ja">日本語</option>
            <option value="ko">한국어</option>
          </select>
        </div>
        <span>{{ $t('welcome') }}, {{ userStore.user?.firstName || 'Admin' }}</span>
        <button @click="logout" class="logout-btn">{{ $t('logout') }}</button>
      </div>
    </div>

    <div class="admin-content">
      <!-- 统计卡片 -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">👥</div>
          <div class="stat-info">
            <h3>{{ stats.users.total }}</h3>
            <p>{{ $t('totalUsers') }}</p>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">📋</div>
          <div class="stat-info">
            <h3>{{ stats.applications.total }}</h3>
            <p>{{ $t('totalApplications') }}</p>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">📊</div>
          <div class="stat-info">
            <h3>{{ stats.reports.total }}</h3>
            <p>{{ $t('totalReports') }}</p>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">⚡</div>
          <div class="stat-info">
            <h3>{{ stats.applications.processing }}</h3>
            <p>{{ $t('processing') }}</p>
          </div>
        </div>
      </div>

      <!-- 管理功能区 -->
      <div class="admin-sections">
        <div class="admin-section">
          <h2>{{ $t('userManagement') }}</h2>
          <div class="section-content">
            <div class="user-stats">
              <p>{{ $t('normalUser') }}: {{ stats.users.user }}</p>
              <p>{{ $t('administrator') }}: {{ stats.users.admin }}</p>
            </div>
            <button @click="activeTab = 'users'" class="section-btn" data-tab="users">
              {{ $t('manageUsers') }}
            </button>
          </div>
        </div>

        <div class="admin-section">
          <h2>{{ $t('applicationManagement') }}</h2>
          <div class="section-content">
            <div class="application-stats">
              <p>{{ $t('pending') }}: {{ stats.applications.pending }}</p>
              <p>{{ $t('completed') }}: {{ stats.applications.completed }}</p>
              <p>{{ $t('failed') }}: {{ stats.applications.failed }}</p>
            </div>
            <button @click="activeTab = 'applications'" class="section-btn" data-tab="applications">
              {{ $t('manageApplications') }}
            </button>
          </div>
        </div>

        <div class="admin-section">
          <h2>{{ $t('systemStatus') }}</h2>
          <div class="section-content">
            <div class="system-info">
              <p>
                {{ $t('status') }}: <span class="status-healthy">{{ $t('healthy') }}</span>
              </p>
              <p>{{ $t('uptime') }}: {{ formatUptime(systemHealth.uptime) }}</p>
              <p>
                {{ $t('memoryUsage') }}: {{ systemHealth.memory.used }}MB /
                {{ systemHealth.memory.total }}MB
              </p>
            </div>
            <button @click="checkSystemHealth" class="section-btn">{{ $t('checkSystem') }}</button>
          </div>
        </div>
      </div>

      <!-- 详细管理界面 -->
      <div v-if="activeTab === 'users'" class="management-panel">
        <div class="section-header">
          <h2>{{ $t('userManagement') }}</h2>
          <div class="header-actions">
            <button
              v-if="userStore.user?.role === 'superadmin'"
              @click="showCreateUserModal = true"
              class="create-btn"
            >
              {{ $t('createUser') }}
            </button>
            <button @click="() => fetchUsers()" class="refresh-btn">{{ $t('refresh') }}</button>
          </div>
        </div>

        <!-- 用户筛选器 -->
        <div class="filters">
          <select v-model="userRoleFilter" @change="fetchUsers(1)">
            <option value="">{{ $t('allRoles') }}</option>
            <option value="user">{{ $t('normalUser') }}</option>
            <option value="admin">{{ $t('administrator') }}</option>
            <option value="superadmin">{{ $t('superAdmin') }}</option>
          </select>
          <select v-model="userStatusFilter" @change="fetchUsers(1)">
            <option value="">{{ $t('allStatuses') }}</option>
            <option value="active">{{ $t('active') }}</option>
            <option value="inactive">{{ $t('inactive') }}</option>
          </select>
        </div>

        <div class="users-list">
          <div v-if="loading" class="loading">{{ $t('loading') }}</div>
          <div v-else-if="users.length === 0" class="no-data">{{ $t('noUsersFound') }}</div>
          <div v-else>
            <div v-for="user in users" :key="user.id" class="user-item enhanced">
              <div class="user-info">
                <div class="user-header">
                  <strong class="user-name">{{ user.firstName }} {{ user.lastName }}</strong>
                  <span class="user-role" :class="user.role">{{ getRoleText(user.role) }}</span>
                </div>
                <p class="user-email">{{ user.email }}</p>
                <div class="user-meta">
                  <span
                    class="user-status"
                    :class="{ active: user.isActive, inactive: !user.isActive }"
                  >
                    {{ user.isActive ? $t('active') : $t('inactive') }}
                  </span>
                  <span class="user-org">{{ user.organization || $t('organization') }}</span>
                </div>
                <p class="user-date">
                  {{ $t('registrationTime') }}: {{ formatDate(user.createdAt) }}
                </p>
              </div>
              <div class="user-actions">
                <button @click="viewUserDetails(user)" class="action-btn view-btn">
                  {{ $t('viewDetails') }}
                </button>
                <button
                  v-if="canEditUser(user)"
                  @click="editUser(user)"
                  class="action-btn edit-btn"
                >
                  {{ $t('edit') }}
                </button>
                <button
                  v-if="canToggleUserStatus(user)"
                  @click="toggleUserStatus(user)"
                  class="action-btn"
                  :class="user.isActive ? 'deactivate-btn' : 'activate-btn'"
                >
                  {{ user.isActive ? $t('deactivate') : $t('activate') }}
                </button>
                <button
                  v-if="canResetPassword(user)"
                  @click="resetUserPassword(user)"
                  class="action-btn reset-btn"
                >
                  {{ $t('resetPassword') }}
                </button>
                <button
                  v-if="canDeleteUser(user)"
                  @click="deleteUser(user)"
                  class="action-btn delete-btn"
                >
                  {{ $t('delete') }}
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="userPagination && userPagination.totalPages > 1" class="pagination">
          <button
            @click="fetchUsers(userPagination.page - 1)"
            :disabled="userPagination.page === 1"
            class="page-btn"
          >
            {{ $t('previousPage') }}
          </button>
          <span class="page-info">
            {{ $t('page') }} {{ userPagination.page }} {{ $t('of') }}
            {{ userPagination.totalPages }}
          </span>
          <button
            @click="fetchUsers(userPagination.page + 1)"
            :disabled="userPagination.page === userPagination.totalPages"
            class="page-btn"
          >
            {{ $t('nextPage') }}
          </button>
        </div>
      </div>

      <div v-if="activeTab === 'applications'" class="management-panel">
        <h2>{{ $t('applicationManagement') }}</h2>

        <!-- 筛选器 -->
        <div class="filters">
          <select v-model="statusFilter" @change="fetchApplications(1)" class="filter-select">
            <option value="">{{ $t('allStatuses') }}</option>
            <option value="pending">{{ $t('pending') }}</option>
            <option value="processing">{{ $t('processing') }}</option>
            <option value="completed">{{ $t('completed') }}</option>
            <option value="failed">{{ $t('failed') }}</option>
            <option value="cancelled">{{ $t('cancelled') }}</option>
          </select>
          <button @click="fetchApplications(1)" class="refresh-btn">{{ $t('refresh') }}</button>
        </div>

        <div class="applications-list">
          <div v-if="loading" class="loading">{{ $t('loading') }}</div>
          <div v-else-if="applications.length === 0" class="no-data">
            {{ $t('noApplicationsFound') }}
          </div>
          <div v-else>
            <div v-for="app in applications" :key="app.id" class="application-item">
              <div class="app-info">
                <div class="app-header">
                  <strong>{{ $t('applicationNumber') }} #{{ app.applicationId }}</strong>
                  <span class="app-status" :class="app.status">{{
                    getStatusText(app.status)
                  }}</span>
                </div>
                <div class="app-details">
                  <p>
                    <strong>{{ $t('userInfo') }}:</strong> {{ app.user?.email }}
                  </p>
                  <p>
                    <strong>{{ $t('analysisType') }}:</strong> {{ app.analysisType }}
                  </p>
                  <p>
                    <strong>{{ $t('createdAt') }}:</strong> {{ formatDate(app.createdAt) }}
                  </p>
                  <p>
                    <strong>{{ $t('estimatedCost') }}:</strong> ${{ app.estimatedCost }}
                  </p>
                  <p v-if="app.files">
                    <strong>{{ $t('uploadedFiles') }}:</strong> {{ app.files.length }}
                  </p>
                  <p v-if="app.notes">
                    <strong>{{ $t('notes') }}:</strong> {{ app.notes }}
                  </p>
                </div>
              </div>
              <div class="app-actions">
                <button
                  v-if="app.status !== 'completed' && app.status !== 'cancelled'"
                  @click="openCompleteModal(app)"
                  class="complete-btn"
                >
                  {{ $t('completeApplication') }}
                </button>
                <button @click="viewApplicationDetails(app)" class="details-btn">
                  {{ $t('viewDetails') }}
                </button>
                <button
                  @click="deleteApplication(app)"
                  class="delete-btn"
                  v-if="app.status === 'cancelled'"
                >
                  {{ $t('delete') }}
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="pagination && pagination.totalPages > 1" class="pagination">
          <button
            @click="changePage(pagination.page - 1)"
            :disabled="!pagination.hasPrev"
            class="page-btn"
          >
            {{ $t('previousPage') }}
          </button>
          <span class="page-info">
            {{ $t('page') }} {{ pagination.page }} {{ $t('of') }} {{ pagination.totalPages }}
          </span>
          <button
            @click="changePage(pagination.page + 1)"
            :disabled="!pagination.hasNext"
            class="page-btn"
          >
            {{ $t('nextPage') }}
          </button>
        </div>
      </div>

      <!-- 申请详情模态框 -->
      <div v-if="selectedApplication" class="modal-overlay" @click="closeModal">
        <div class="modal-content" @click.stop>
          <div class="modal-header">
            <h3>{{ $t('applicationDetails') }} #{{ selectedApplication.applicationId }}</h3>
            <button @click="closeModal" class="close-btn">×</button>
          </div>
          <div class="modal-body">
            <div class="detail-section">
              <h4>{{ $t('basicInfo') }}</h4>
              <p>
                <strong>{{ $t('applicationNumber') }}:</strong>
                {{ selectedApplication.applicationId }}
              </p>
              <p>
                <strong>{{ $t('status') }}:</strong>
                <span class="app-status" :class="selectedApplication.status">{{
                  getStatusText(selectedApplication.status)
                }}</span>
              </p>
              <p>
                <strong>{{ $t('analysisType') }}:</strong> {{ selectedApplication.analysisType }}
              </p>
              <p>
                <strong>{{ $t('estimatedCost') }}:</strong> ${{ selectedApplication.estimatedCost }}
              </p>
              <p>
                <strong>{{ $t('createdAt') }}:</strong>
                {{ formatDate(selectedApplication.createdAt) }}
              </p>
              <p>
                <strong>{{ $t('updatedAt') }}:</strong>
                {{ formatDate(selectedApplication.updatedAt) }}
              </p>
            </div>

            <div class="detail-section">
              <h4>{{ $t('userInfo') }}</h4>
              <p>
                <strong>{{ $t('email') }}:</strong> {{ selectedApplication.user?.email }}
              </p>
              <p>
                <strong>{{ $t('name') }}:</strong> {{ selectedApplication.user?.firstName }}
                {{ selectedApplication.user?.lastName }}
              </p>
              <p v-if="selectedApplication.user?.organization">
                <strong>{{ $t('organization') }}:</strong>
                {{ selectedApplication.user.organization }}
              </p>
              <p v-if="selectedApplication.contactInfo?.phone">
                <strong>{{ $t('phone') }}:</strong> {{ selectedApplication.contactInfo.phone }}
              </p>
            </div>

            <div
              class="detail-section"
              v-if="selectedApplication.files && selectedApplication.files.length > 0"
            >
              <h4>{{ $t('uploadedFiles') }}</h4>
              <div v-for="file in selectedApplication.files" :key="file.filename" class="file-item">
                <p>
                  <strong>{{ $t('fileName') }}:</strong> {{ file.originalName }}
                </p>
                <p>
                  <strong>{{ $t('fileType') }}:</strong> {{ file.fileType }}
                </p>
                <p>
                  <strong>{{ $t('fileSize') }}:</strong> {{ formatFileSize(file.size) }}
                </p>
              </div>
            </div>

            <div class="detail-section" v-if="selectedApplication.parameters">
              <h4>{{ $t('analysisParameters') }}</h4>
              <p>
                <strong>{{ $t('algorithm') }}:</strong>
                {{ selectedApplication.parameters.algorithm }}
              </p>
              <p>
                <strong>{{ $t('confidence') }}:</strong>
                {{ selectedApplication.parameters.confidence }}
              </p>
              <p v-if="selectedApplication.parameters.maxIterations">
                <strong>{{ $t('maxIterations') }}:</strong>
                {{ selectedApplication.parameters.maxIterations }}
              </p>
            </div>

            <div class="detail-section" v-if="selectedApplication.notes">
              <h4>{{ $t('notes') }}</h4>
              <p>{{ selectedApplication.notes }}</p>
            </div>

            <!-- 报告文件部分 -->
            <div
              class="detail-section"
              v-if="selectedApplication.previewReportFile || selectedApplication.fullReportFile"
            >
              <h4>{{ $t('reportFiles') }}</h4>
              <div v-if="selectedApplication.previewReportFile" class="file-item report-file">
                <div class="file-info">
                  <p>
                    <strong>{{ $t('previewReport') }}:</strong>
                    {{ selectedApplication.previewReportFile.originalName }}
                  </p>
                  <p>
                    <strong>{{ $t('fileSize') }}:</strong>
                    {{ formatFileSize(selectedApplication.previewReportFile.size) }}
                  </p>
                  <p>
                    <strong>{{ $t('uploadTime') }}:</strong>
                    {{ formatDate(selectedApplication.previewReportFile.uploadedAt) }}
                  </p>
                </div>
                <button
                  @click="downloadReport(selectedApplication.id, 'preview')"
                  class="download-btn"
                >
                  {{ $t('downloadPreviewReport') }}
                </button>
              </div>
              <div v-if="selectedApplication.fullReportFile" class="file-item report-file">
                <div class="file-info">
                  <p>
                    <strong>{{ $t('fullReport') }}:</strong>
                    {{ selectedApplication.fullReportFile.originalName }}
                  </p>
                  <p>
                    <strong>{{ $t('fileSize') }}:</strong>
                    {{ formatFileSize(selectedApplication.fullReportFile.size) }}
                  </p>
                  <p>
                    <strong>{{ $t('reportPrice') }}:</strong> ${{
                      selectedApplication.reportPrice || 0
                    }}
                  </p>
                  <p>
                    <strong>{{ $t('paymentStatus') }}:</strong>
                    {{ getPaymentStatusText(selectedApplication.reportPaymentStatus || 'unpaid') }}
                  </p>
                </div>
                <button
                  @click="downloadReport(selectedApplication.id, 'full')"
                  class="download-btn"
                >
                  {{ $t('downloadFullReport') }}
                </button>
              </div>
            </div>

            <!-- 报告文件显示 -->
            <div
              class="detail-section"
              v-if="selectedApplication.previewReportFile || selectedApplication.fullReportFile"
            >
              <h4>{{ $t('analysisReport') }}</h4>

              <!-- 预览报告 -->
              <div v-if="selectedApplication.previewReportFile" class="report-item">
                <div class="report-header">
                  <h5>📄 {{ $t('previewReport') }} ({{ $t('freeAccess') }})</h5>
                  <span class="report-status free">{{ $t('free') }}</span>
                </div>
                <div class="report-details">
                  <p>
                    <strong>{{ $t('fileName') }}:</strong>
                    {{ selectedApplication.previewReportFile.originalName }}
                  </p>
                  <p>
                    <strong>{{ $t('fileSize') }}:</strong>
                    {{ formatFileSize(selectedApplication.previewReportFile.size) }}
                  </p>
                  <p>
                    <strong>{{ $t('uploadTime') }}:</strong>
                    {{ formatDate(selectedApplication.previewReportFile.uploadedAt) }}
                  </p>
                  <button
                    @click="downloadReport(selectedApplication.id, 'preview')"
                    class="download-btn preview"
                  >
                    📥 下载预览报告
                  </button>
                </div>
              </div>

              <!-- 完整报告 -->
              <div v-if="selectedApplication.fullReportFile" class="report-item">
                <div class="report-header">
                  <h5>📊 完整报告 (付费下载)</h5>
                  <span class="report-status" :class="selectedApplication.reportPaymentStatus">
                    {{ getPaymentStatusText(selectedApplication.reportPaymentStatus || 'unpaid') }}
                  </span>
                </div>
                <div class="report-details">
                  <p>
                    <strong>文件名:</strong> {{ selectedApplication.fullReportFile.originalName }}
                  </p>
                  <p>
                    <strong>大小:</strong>
                    {{ formatFileSize(selectedApplication.fullReportFile.size) }}
                  </p>
                  <p><strong>价格:</strong> ${{ selectedApplication.reportPrice || 0 }}</p>
                  <p>
                    <strong>上传时间:</strong>
                    {{ formatDate(selectedApplication.fullReportFile.uploadedAt) }}
                  </p>
                  <button
                    @click="downloadReport(selectedApplication.id, 'full')"
                    class="download-btn full"
                  >
                    💰 下载完整报告
                  </button>
                </div>
              </div>
            </div>

            <!-- 附加文件显示 -->
            <div
              class="detail-section"
              v-if="selectedApplication.resultFiles && selectedApplication.resultFiles.length > 0"
            >
              <h4>附加文件</h4>
              <div
                v-for="file in selectedApplication.resultFiles"
                :key="file.filename"
                class="file-item"
              >
                <p><strong>文件名:</strong> {{ file.originalName }}</p>
                <p><strong>类型:</strong> {{ file.type || 'additional' }}</p>
                <p><strong>大小:</strong> {{ formatFileSize(file.size) }}</p>
                <p><strong>上传时间:</strong> {{ formatDate(file.uploadedAt) }}</p>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <!-- 完成申请功能 -->
            <div
              v-if="
                selectedApplication.status !== 'completed' &&
                selectedApplication.status !== 'cancelled'
              "
              class="complete-application"
            >
              <h4>完成申请</h4>
              <div class="complete-form">
                <div class="file-upload-group">
                  <label>预览报告 (必需) *</label>
                  <input
                    type="file"
                    ref="previewReportFileRef"
                    accept=".pdf,.doc,.docx"
                    class="file-input"
                  />
                </div>
                <div class="file-upload-group">
                  <label>完整报告 (必需) *</label>
                  <input
                    type="file"
                    ref="fullReportFileRef"
                    accept=".pdf,.doc,.docx"
                    class="file-input"
                  />
                </div>
                <div class="file-upload-group">
                  <label>附加文件 (可选)</label>
                  <input
                    type="file"
                    ref="additionalFiles"
                    multiple
                    accept=".pdf,.txt,.csv,.xlsx,.zip,.doc,.docx"
                    class="file-input"
                  />
                </div>
                <div class="form-row">
                  <div class="form-group">
                    <label>报告价格 ($)</label>
                    <input
                      v-model="completionForm.reportPrice"
                      type="number"
                      step="0.01"
                      min="0"
                      class="form-input"
                      placeholder="0.00"
                    />
                  </div>
                  <div class="form-group">
                    <label>付费状态</label>
                    <select v-model="completionForm.paymentStatus" class="form-select">
                      <option value="unpaid">未付费</option>
                      <option value="paid">已付费</option>
                      <option value="free">免费</option>
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <label>完成备注</label>
                  <textarea
                    v-model="completionForm.notes"
                    placeholder="添加完成备注..."
                    class="notes-textarea"
                    rows="3"
                  ></textarea>
                </div>
                <button @click="completeApplication" class="complete-btn">完成申请</button>
              </div>
            </div>

            <!-- 常规状态更新 -->
            <div v-else class="status-update">
              <select v-model="selectedApplication.newStatus" class="status-select">
                <option value="pending">待处理</option>
                <option value="processing">处理中</option>
                <option value="completed">已完成</option>
                <option value="failed">失败</option>
                <option value="cancelled">已取消</option>
              </select>
              <input v-model="statusNotes" placeholder="添加备注..." class="notes-input" />
              <button @click="updateApplicationStatus(selectedApplication)" class="update-btn">
                更新状态
              </button>
            </div>

            <!-- 附加文件上传 -->
            <div class="file-upload" v-if="selectedApplication.status !== 'completed'">
              <input
                type="file"
                ref="resultFiles"
                multiple
                accept=".pdf,.txt,.csv,.xlsx,.zip"
                class="file-input"
              />
              <button @click="uploadResultFiles" class="upload-btn">上传附加文件</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 创建用户模态框 -->
      <div v-if="showCreateUserModal" class="modal-overlay" @click="closeCreateUserModal">
        <div class="modal-content" @click.stop>
          <div class="modal-header">
            <h3>创建新用户</h3>
            <button @click="closeCreateUserModal" class="close-btn">×</button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="createUser" class="user-form">
              <div class="form-row">
                <div class="form-group">
                  <label>邮箱 *</label>
                  <input v-model="newUser.email" type="email" required class="form-input" />
                </div>
                <div class="form-group">
                  <label>密码 *</label>
                  <input
                    v-model="newUser.password"
                    type="password"
                    required
                    minlength="6"
                    class="form-input"
                  />
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label>名字 *</label>
                  <input v-model="newUser.firstName" type="text" required class="form-input" />
                </div>
                <div class="form-group">
                  <label>姓氏 *</label>
                  <input v-model="newUser.lastName" type="text" required class="form-input" />
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label>角色 *</label>
                  <select v-model="newUser.role" required class="form-select">
                    <option value="user">普通用户</option>
                    <option value="admin">管理员</option>
                    <option v-if="userStore.user?.role === 'superadmin'" value="superadmin">
                      超级管理员
                    </option>
                  </select>
                </div>
                <div class="form-group">
                  <label>组织</label>
                  <input v-model="newUser.organization" type="text" class="form-input" />
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label>电话</label>
                  <input v-model="newUser.phone" type="tel" class="form-input" />
                </div>
                <div class="form-group">
                  <label>国家</label>
                  <input v-model="newUser.country" type="text" class="form-input" />
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label>
                    <input v-model="newUser.isEmailVerified" type="checkbox" />
                    邮箱已验证
                  </label>
                </div>
                <div class="form-group">
                  <label>
                    <input v-model="newUser.isActive" type="checkbox" />
                    账户激活
                  </label>
                </div>
              </div>
              <div class="form-actions">
                <button type="button" @click="closeCreateUserModal" class="cancel-btn">取消</button>
                <button type="submit" class="submit-btn">创建用户</button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- 编辑用户模态框 -->
      <div v-if="showEditUserModal" class="modal-overlay" @click="closeEditUserModal">
        <div class="modal-content" @click.stop>
          <div class="modal-header">
            <h3>编辑用户</h3>
            <button @click="closeEditUserModal" class="close-btn">×</button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="updateUser" class="user-form">
              <div class="form-row">
                <div class="form-group">
                  <label>邮箱 *</label>
                  <input v-model="editingUser.email" type="email" required class="form-input" />
                </div>
                <div class="form-group">
                  <label>角色 *</label>
                  <select v-model="editingUser.role" required class="form-select">
                    <option value="user">普通用户</option>
                    <option value="admin">管理员</option>
                    <option v-if="userStore.user?.role === 'superadmin'" value="superadmin">
                      超级管理员
                    </option>
                  </select>
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label>名字 *</label>
                  <input v-model="editingUser.firstName" type="text" required class="form-input" />
                </div>
                <div class="form-group">
                  <label>姓氏 *</label>
                  <input v-model="editingUser.lastName" type="text" required class="form-input" />
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label>组织</label>
                  <input v-model="editingUser.organization" type="text" class="form-input" />
                </div>
                <div class="form-group">
                  <label>电话</label>
                  <input v-model="editingUser.phone" type="tel" class="form-input" />
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label>国家</label>
                  <input v-model="editingUser.country" type="text" class="form-input" />
                </div>
                <div class="form-group">
                  <label>语言</label>
                  <select v-model="editingUser.language" class="form-select">
                    <option value="zh">中文</option>
                    <option value="en">English</option>
                    <option value="es">Español</option>
                    <option value="fr">Français</option>
                    <option value="de">Deutsch</option>
                    <option value="ja">日本語</option>
                    <option value="ko">한국어</option>
                  </select>
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label>
                    <input v-model="editingUser.isEmailVerified" type="checkbox" />
                    邮箱已验证
                  </label>
                </div>
                <div class="form-group">
                  <label>
                    <input v-model="editingUser.isActive" type="checkbox" />
                    账户激活
                  </label>
                </div>
              </div>
              <div class="form-actions">
                <button type="button" @click="closeEditUserModal" class="cancel-btn">取消</button>
                <button type="submit" class="submit-btn">更新用户</button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- 用户详情模态框 -->
      <div v-if="selectedUser" class="modal-overlay" @click="closeUserDetailsModal">
        <div class="modal-content" @click.stop>
          <div class="modal-header">
            <h3>用户详情</h3>
            <button @click="closeUserDetailsModal" class="close-btn">×</button>
          </div>
          <div class="modal-body">
            <div class="detail-section">
              <h4>基本信息</h4>
              <p><strong>姓名:</strong> {{ selectedUser.firstName }} {{ selectedUser.lastName }}</p>
              <p><strong>邮箱:</strong> {{ selectedUser.email }}</p>
              <p><strong>角色:</strong> {{ getRoleText(selectedUser.role) }}</p>
              <p><strong>状态:</strong> {{ selectedUser.isActive ? '活跃' : '停用' }}</p>
              <p>
                <strong>邮箱验证:</strong> {{ selectedUser.isEmailVerified ? '已验证' : '未验证' }}
              </p>
            </div>
            <div
              class="detail-section"
              v-if="selectedUser.organization || selectedUser.phone || selectedUser.country"
            >
              <h4>联系信息</h4>
              <p v-if="selectedUser.organization">
                <strong>组织:</strong> {{ selectedUser.organization }}
              </p>
              <p v-if="selectedUser.phone"><strong>电话:</strong> {{ selectedUser.phone }}</p>
              <p v-if="selectedUser.country"><strong>国家:</strong> {{ selectedUser.country }}</p>
            </div>
            <div class="detail-section">
              <h4>账户信息</h4>
              <p><strong>注册时间:</strong> {{ formatDate(selectedUser.createdAt) }}</p>
              <p><strong>最后更新:</strong> {{ formatDate(selectedUser.updatedAt) }}</p>
              <p><strong>语言:</strong> {{ selectedUser.language || 'zh' }}</p>
              <p><strong>订阅计划:</strong> {{ selectedUser.subscriptionPlan || 'free' }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/stores/counter'
import { adminAPI, handleApiError } from '@/services/api'
import type { User, Application, PaginationResponse } from '@/types/api'
import { useLanguage } from '@/utils/language'

export default defineComponent({
  name: 'AdminDashboardView',
  setup() {
    const router = useRouter()
    const { t } = useI18n()
    const userStore = useUserStore()
    const { locale, changeLanguage: setLanguage } = useLanguage()
    const loading = ref(false)
    const activeTab = ref('applications') // 默认显示申请管理

    // 语言切换相关
    const currentLocale = ref(locale.value)

    const changeLanguage = (event: Event) => {
      const target = event.target as HTMLSelectElement
      const newLocale = target.value
      setLanguage(newLocale)
      currentLocale.value = newLocale
    }

    const stats = reactive({
      users: { total: 0, user: 0, admin: 0 },
      applications: { total: 0, pending: 0, processing: 0, completed: 0, failed: 0, cancelled: 0 },
      reports: { total: 0, generating: 0, ready: 0, error: 0 },
    })

    const systemHealth = reactive({
      status: 'healthy',
      uptime: 0,
      memory: { used: 0, total: 0 },
    })

    const users = ref<User[]>([])
    const applications = ref<Application[]>([])
    const pagination = ref<PaginationResponse | null>(null)
    const statusFilter = ref('')
    const selectedApplication = ref<Application | null>(null)
    const statusNotes = ref('')

    // 用户管理相关状态
    const userPagination = ref<PaginationResponse | null>(null)
    const userRoleFilter = ref('')
    const userStatusFilter = ref('')
    const showCreateUserModal = ref(false)
    const showEditUserModal = ref(false)
    const selectedUser = ref<User | null>(null)

    // 新用户表单数据
    const newUser = reactive({
      email: '',
      password: '',
      firstName: '',
      lastName: '',
      role: 'user',
      organization: '',
      phone: '',
      country: '',
      isEmailVerified: false,
      isActive: true,
    })

    // 编辑用户表单数据
    const editingUser = reactive({
      id: null,
      email: '',
      firstName: '',
      lastName: '',
      role: 'user',
      organization: '',
      phone: '',
      country: '',
      language: 'zh',
      isEmailVerified: false,
      isActive: true,
    })

    // 完成申请表单数据
    const completionForm = reactive({
      reportPrice: 0,
      paymentStatus: 'unpaid',
      notes: '',
    })

    // 文件上传ref
    const previewReportFileRef = ref<HTMLInputElement>()
    const fullReportFileRef = ref<HTMLInputElement>()

    const fetchStats = async () => {
      try {
        const response = await adminAPI.getDashboardStats()
        console.log('📊 Stats API response:', response.data)
        // 后端返回的是 { stats: {...} }，所以需要访问 response.data.stats
        if (response.data.stats) {
          Object.assign(stats, response.data.stats)
        } else {
          Object.assign(stats, response.data)
        }
        console.log('📊 Updated stats:', stats)
      } catch (error) {
        console.error('Failed to fetch stats:', error)
        const apiError = handleApiError(error)
        console.error('API Error:', apiError)
      }
    }

    const fetchUsers = async (page: number = 1) => {
      loading.value = true
      try {
        const params: any = {
          page: page.toString(),
          limit: '20',
        }

        if (userRoleFilter.value) {
          params.role = userRoleFilter.value
        }

        if (userStatusFilter.value) {
          params.status = userStatusFilter.value
        }

        const response = await adminAPI.getUsers(params)
        users.value = response.data.users
        userPagination.value = response.data.pagination
      } catch (error) {
        console.error('Failed to fetch users:', error)
        const apiError = handleApiError(error)
        console.error('API Error:', apiError)
      } finally {
        loading.value = false
      }
    }

    const fetchApplications = async (page: number = 1) => {
      console.log('🔍 fetchApplications 被调用', {
        page,
        statusFilter: statusFilter.value,
        activeTab: activeTab.value,
      })
      loading.value = true
      try {
        const params: any = {
          page: page.toString(),
          limit: '20',
        }

        if (statusFilter.value) {
          params.status = statusFilter.value
        }

        console.log('📡 API 请求参数:', params)
        console.log(
          '🔑 Token:',
          userStore.token ? `${userStore.token.substring(0, 20)}...` : 'null',
        )

        const response = await adminAPI.getApplications(params)
        console.log('📊 API 响应状态: 成功')
        console.log('📋 API 响应数据:', response.data)

        applications.value = response.data.applications.map((app: any) => ({
          ...app,
          newStatus: app.status, // 初始化新状态为当前状态
        }))
        pagination.value = response.data.pagination

        console.log('✅ 申请数据已更新:', applications.value.length, '个申请')
      } catch (error) {
        console.error('❌ fetchApplications 错误:', error)
        const apiError = handleApiError(error)
        console.error('❌ API 错误详情:', apiError)
      } finally {
        loading.value = false
      }
    }

    const checkSystemHealth = async () => {
      try {
        const response = await adminAPI.getSystemHealth()
        Object.assign(systemHealth, response.data)
      } catch (error) {
        console.error('Failed to check system health:', error)
        const apiError = handleApiError(error)
        console.error('API Error:', apiError)
      }
    }

    const promoteUser = async (userId: number) => {
      if (!confirm('确定要将此用户提升为管理员吗？')) return

      try {
        await adminAPI.promoteUser(userId, { role: 'admin' })
        await fetchUsers()
        await fetchStats()
      } catch (error) {
        console.error('Failed to promote user:', error)
        const apiError = handleApiError(error)
        alert(`提升用户失败: ${apiError.message}`)
      }
    }

    const deactivateUser = async (userId: number) => {
      if (!confirm('确定要停用此用户账户吗？')) return

      try {
        const response = await fetch(`/api/admin/users/${userId}/deactivate`, {
          method: 'PATCH',
          headers: {
            Authorization: `Bearer ${userStore.token}`,
          },
        })

        if (response.ok) {
          await fetchUsers()
        }
      } catch (error) {
        console.error('Failed to deactivate user:', error)
      }
    }

    const logout = async () => {
      await userStore.logout()
      router.push('/login')
    }

    const getRoleText = (role: string) => {
      const roleMap: { [key: string]: string } = {
        user: '普通用户',
        admin: '管理员',
        superadmin: '超级管理员',
      }
      return roleMap[role] || role
    }

    // 权限检查方法
    const canEditUser = (user: any) => {
      if (!userStore.user) return false
      if (user.id === userStore.user.id) return false // 不能编辑自己
      if (userStore.user.role === 'superadmin') return true // 超级管理员可以编辑所有用户
      if (userStore.user.role === 'admin' && user.role !== 'superadmin') return true // 管理员可以编辑非超级管理员
      return false
    }

    const canToggleUserStatus = (user: any) => {
      if (!userStore.user) return false
      if (user.id === userStore.user.id) return false // 不能操作自己
      if (userStore.user.role === 'superadmin') return true // 超级管理员可以操作所有用户
      if (userStore.user.role === 'admin' && user.role !== 'superadmin') return true // 管理员可以操作非超级管理员
      return false
    }

    const canResetPassword = (user: any) => {
      if (!userStore.user) return false
      return userStore.user.role === 'superadmin' && user.id !== userStore.user.id
    }

    const canDeleteUser = (user: any) => {
      if (!userStore.user) return false
      return userStore.user.role === 'superadmin' && user.id !== userStore.user.id
    }

    // 用户管理方法
    const createUser = async () => {
      try {
        await adminAPI.createUser(newUser)
        alert('用户创建成功')
        closeCreateUserModal()
        fetchUsers()
      } catch (error) {
        console.error('Create user error:', error)
        const apiError = handleApiError(error)
        alert(`创建用户失败: ${apiError.message}`)
      }
    }

    const editUser = (user: any) => {
      Object.assign(editingUser, {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        organization: user.organization || '',
        phone: user.phone || '',
        country: user.country || '',
        language: user.language || 'zh',
        isEmailVerified: user.isEmailVerified,
        isActive: user.isActive,
      })
      showEditUserModal.value = true
    }

    const updateUser = async () => {
      try {
        const { id, ...updateData } = editingUser
        if (!id) throw new Error('User ID is required')
        await adminAPI.updateUser(id, updateData)
        alert('用户更新成功')
        closeEditUserModal()
        fetchUsers()
      } catch (error) {
        console.error('Update user error:', error)
        const apiError = handleApiError(error)
        alert(`更新用户失败: ${apiError.message}`)
      }
    }

    const viewUserDetails = (user: any) => {
      selectedUser.value = user
    }

    const toggleUserStatus = async (user: User) => {
      const action = user.isActive ? 'deactivate' : 'activate'
      const actionText = user.isActive ? '停用' : '激活'

      if (!confirm(`确定要${actionText}用户 ${user.email} 吗？`)) {
        return
      }

      try {
        await adminAPI.toggleUserStatus(user.id, action)
        alert(`用户${actionText}成功`)
        fetchUsers()
      } catch (error) {
        console.error(`${actionText} user error:`, error)
        const apiError = handleApiError(error)
        alert(`${actionText}用户失败: ${apiError.message}`)
      }
    }

    const resetUserPassword = async (user: User) => {
      const newPassword = prompt(`请输入用户 ${user.email} 的新密码 (至少6位):`)
      if (!newPassword || newPassword.length < 6) {
        alert('密码至少需要6位字符')
        return
      }

      try {
        await adminAPI.resetUserPassword(user.id, {
          newPassword,
        })
        alert('密码重置成功')
      } catch (error) {
        console.error('Reset password error:', error)
        const apiError = handleApiError(error)
        alert(`重置密码失败: ${apiError.message}`)
      }
    }

    const deleteUser = async (user: User) => {
      if (!confirm(`确定要删除用户 ${user.email} 吗？此操作不可撤销！`)) {
        return
      }

      try {
        await adminAPI.deleteUser(user.id)
        alert('用户删除成功')
        fetchUsers()
      } catch (error) {
        console.error('Delete user error:', error)
        const apiError = handleApiError(error)
        alert(`删除用户失败: ${apiError.message}`)
      }
    }

    // 模态框控制方法
    const closeCreateUserModal = () => {
      showCreateUserModal.value = false
      // 重置表单
      Object.assign(newUser, {
        email: '',
        password: '',
        firstName: '',
        lastName: '',
        role: 'user',
        organization: '',
        phone: '',
        country: '',
        isEmailVerified: false,
        isActive: true,
      })
    }

    const closeEditUserModal = () => {
      showEditUserModal.value = false
    }

    const closeUserDetailsModal = () => {
      selectedUser.value = null
    }

    // 完成申请方法
    const completeApplication = async () => {
      try {
        const previewFile = previewReportFileRef.value?.files?.[0]
        const fullFile = fullReportFileRef.value?.files?.[0]

        if (!previewFile) {
          alert('请选择预览报告文件')
          return
        }

        if (!fullFile) {
          alert('请选择完整报告文件')
          return
        }

        if (!completionForm.reportPrice || completionForm.reportPrice <= 0) {
          alert('请设置有效的报告价格')
          return
        }

        loading.value = true

        const formData = new FormData()
        formData.append('previewReport', previewFile)
        formData.append('fullReport', fullFile)
        formData.append('reportPrice', completionForm.reportPrice.toString())
        formData.append('paymentStatus', completionForm.paymentStatus)
        formData.append('notes', completionForm.notes)

        if (!selectedApplication.value) throw new Error('No application selected')
        const response = await adminAPI.completeApplication(selectedApplication.value.id, formData)

        alert('申请完成成功！报告已上传')

        // 重置表单
        completionForm.reportPrice = 0
        completionForm.paymentStatus = 'unpaid'
        completionForm.notes = ''

        // 清空文件输入
        if (previewReportFileRef.value) previewReportFileRef.value.value = ''
        if (fullReportFileRef.value) fullReportFileRef.value.value = ''

        closeModal()
        fetchApplications()
        fetchStats()
      } catch (error) {
        console.error('Complete application error:', error)
        alert('完成申请时发生错误')
      } finally {
        loading.value = false
      }
    }

    const getStatusText = (status: string) => {
      const statusMap: { [key: string]: string } = {
        pending: '待处理',
        processing: '处理中',
        completed: '已完成',
        failed: '失败',
        cancelled: '已取消',
      }
      return statusMap[status] || status
    }

    const formatDate = (dateString: string) => {
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    const formatUptime = (seconds: number) => {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      return `${hours}小时${minutes}分钟`
    }

    const formatFileSize = (bytes: number) => {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    // 获取付费状态文本
    const getPaymentStatusText = (status: string) => {
      const statusMap: Record<string, string> = {
        unpaid: '未付费',
        paid: '已付费',
        pending: '待付费',
        refunded: '已退款',
      }
      return statusMap[status] || '未付费'
    }

    // 下载报告文件
    const downloadReport = async (applicationId: number, reportType: 'preview' | 'full') => {
      try {
        loading.value = true

        const response = await adminAPI.downloadReport(applicationId, reportType)

        // 获取文件名
        const contentDisposition = response.headers['content-disposition']
        let filename = `${reportType}_report_${applicationId}.pdf`

        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename="(.+)"/)
          if (filenameMatch) {
            filename = filenameMatch[1]
          }
        }

        // 创建下载链接
        const blob = response.data
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = filename
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        alert(`${reportType === 'preview' ? '预览' : '完整'}报告下载成功`)
      } catch (error) {
        console.error('下载报告失败:', error)
        const apiError = handleApiError(error)
        alert('下载报告失败: ' + apiError.message)
      } finally {
        loading.value = false
      }
    }

    const changePage = (page: number) => {
      if (page >= 1 && pagination.value && page <= pagination.value.totalPages) {
        fetchApplications(page)
      }
    }

    // 仅在详情模态框中使用的状态更新方法
    const updateApplicationStatus = async (application: Application) => {
      if (!application.newStatus || application.newStatus === application.status) {
        alert('请选择新的状态')
        return
      }

      try {
        await adminAPI.updateApplicationStatus(application.id, {
          status: application.newStatus,
          notes: statusNotes.value,
        })

        await fetchApplications()
        await fetchStats()
        statusNotes.value = ''

        // 更新当前选中的申请状态
        if (selectedApplication.value) {
          selectedApplication.value.status = application.newStatus
          selectedApplication.value.newStatus = application.newStatus
        }

        alert('状态更新成功')
      } catch (error) {
        console.error('Failed to update application status:', error)
        const apiError = handleApiError(error)
        alert(`更新失败: ${apiError.message}`)
      }
    }

    const viewApplicationDetails = async (application: Application) => {
      try {
        const response = await adminAPI.getApplication(application.id)
        selectedApplication.value = {
          ...response.data.application,
          newStatus: response.data.application.status,
        }
      } catch (error) {
        console.error('Failed to fetch application details:', error)
        const apiError = handleApiError(error)
        alert(`获取申请详情失败: ${apiError.message}`)
      }
    }

    const openCompleteModal = async (application: Application) => {
      await viewApplicationDetails(application)
      // 重置完成表单
      completionForm.reportPrice = 0
      completionForm.paymentStatus = 'unpaid'
      completionForm.notes = ''
    }

    const closeModal = () => {
      selectedApplication.value = null
      statusNotes.value = ''
    }

    const deleteApplication = async (application: Application) => {
      if (!confirm(`确定要删除申请 #${application.applicationId} 吗？此操作不可恢复。`)) {
        return
      }

      try {
        await adminAPI.deleteApplication(application.id)
        await fetchApplications()
        await fetchStats()
        alert('申请删除成功')
      } catch (error) {
        console.error('Failed to delete application:', error)
        const apiError = handleApiError(error)
        alert(`删除失败: ${apiError.message}`)
      }
    }

    const uploadResultFiles = async () => {
      if (!selectedApplication.value) return

      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
        alert('请选择要上传的结果文件')
        return
      }

      const formData = new FormData()
      for (let i = 0; i < fileInput.files.length; i++) {
        formData.append('resultFiles', fileInput.files[i])
      }

      if (statusNotes.value) {
        formData.append('notes', statusNotes.value)
      }

      try {
        await adminAPI.uploadResultFiles(selectedApplication.value.id, formData)

        await fetchApplications()
        await fetchStats()
        statusNotes.value = ''
        fileInput.value = ''
        alert('结果文件上传成功')

        // 刷新申请详情
        await viewApplicationDetails(selectedApplication.value)
      } catch (error) {
        console.error('Failed to upload result files:', error)
        const apiError = handleApiError(error)
        alert(`上传失败: ${apiError.message}`)
      }
    }

    onMounted(() => {
      console.log('🚀 AdminDashboardView 组件已挂载')
      console.log('👤 当前用户:', userStore.user)
      console.log('🔐 认证状态:', userStore.isAuthenticated)

      // 检查管理员权限
      if (
        !userStore.user ||
        (userStore.user.role !== 'admin' && userStore.user.role !== 'superadmin')
      ) {
        console.log('❌ 权限不足，跳转到登录页')
        router.push('/login')
        return
      }

      console.log('✅ 权限验证通过，开始加载数据')
      fetchStats()
      checkSystemHealth()

      // 如果默认显示申请管理，立即加载申请数据
      if (activeTab.value === 'applications') {
        console.log('📋 默认显示申请管理，立即加载申请数据')
        fetchApplications()
      }
    })

    // 监听activeTab变化
    const handleTabChange = () => {
      if (activeTab.value === 'users') {
        fetchUsers()
      } else if (activeTab.value === 'applications') {
        fetchApplications()
      }
    }

    // 使用 watch 监听 activeTab 变化
    watch(activeTab, (newTab, oldTab) => {
      console.log('🔄 activeTab 变化:', { oldTab, newTab })
      if (newTab === 'users') {
        console.log('👥 切换到用户管理，调用 fetchUsers')
        fetchUsers()
      } else if (newTab === 'applications') {
        console.log('📋 切换到申请管理，调用 fetchApplications')
        fetchApplications()
      }
    })

    return {
      userStore,
      loading,
      activeTab,
      stats,
      systemHealth,
      users,
      applications,
      pagination,
      statusFilter,
      selectedApplication,
      statusNotes,
      // 语言切换相关
      currentLocale,
      changeLanguage,
      // 用户管理相关
      userPagination,
      userRoleFilter,
      userStatusFilter,
      showCreateUserModal,
      showEditUserModal,
      selectedUser,
      newUser,
      editingUser,
      completionForm,
      previewReportFileRef,
      fullReportFileRef,
      // 方法
      logout,
      checkSystemHealth,
      promoteUser,
      deactivateUser,
      getRoleText,
      getStatusText,
      formatDate,
      formatUptime,
      formatFileSize,
      getPaymentStatusText,
      downloadReport,
      handleTabChange,
      fetchApplications,
      changePage,
      updateApplicationStatus,
      viewApplicationDetails,
      closeModal,
      deleteApplication,
      uploadResultFiles,
      // 用户管理方法
      fetchUsers,
      canEditUser,
      canToggleUserStatus,
      canResetPassword,
      canDeleteUser,
      createUser,
      editUser,
      updateUser,
      viewUserDetails,
      toggleUserStatus,
      resetUserPassword,
      deleteUser,
      closeCreateUserModal,
      closeEditUserModal,
      closeUserDetailsModal,
      // 申请完成相关方法
      completeApplication,
      openCompleteModal,
    }
  },
})
</script>

<style scoped>
.admin-dashboard {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.admin-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.admin-header h1 {
  margin: 0;
  font-size: 28px;
}

.homepage-link {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  padding: 8px 16px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
}

.homepage-link:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateY(-1px);
}

.homepage-icon {
  font-size: 16px;
}

.admin-user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.admin-user-info .language-switcher {
  margin-right: 10px;
}

.admin-user-info .language-select {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.admin-user-info .language-select:hover {
  background: rgba(255, 255, 255, 0.2);
}

.admin-user-info .language-select option {
  background: #333;
  color: white;
}

.logout-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.admin-content {
  padding: 40px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 20px;
}

.stat-icon {
  font-size: 40px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
}

.stat-info h3 {
  margin: 0 0 5px 0;
  font-size: 32px;
  color: #333;
}

.stat-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.admin-sections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.admin-section {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.admin-section h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 20px;
}

.section-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.user-stats,
.application-stats,
.system-info {
  flex: 1;
}

.user-stats p,
.application-stats p,
.system-info p {
  margin: 5px 0;
  color: #666;
  font-size: 14px;
}

.status-healthy {
  color: #28a745;
  font-weight: bold;
}

.section-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.section-btn:hover {
  transform: translateY(-2px);
}

.management-panel {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.management-panel h2 {
  margin: 0 0 25px 0;
  color: #333;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
}

.user-item,
.application-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.user-item:last-child,
.application-item:last-child {
  border-bottom: none;
}

.user-info,
.app-info {
  flex: 1;
}

.user-role,
.app-status {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  margin-left: 10px;
}

.user-role.admin,
.app-status.completed {
  background: #d4edda;
  color: #155724;
}

.user-role.user,
.app-status.pending {
  background: #fff3cd;
  color: #856404;
}

.app-status.processing {
  background: #cce5ff;
  color: #004085;
}

.app-status.failed {
  background: #f8d7da;
  color: #721c24;
}

.user-actions {
  display: flex;
  gap: 10px;
}

.promote-btn,
.deactivate-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.promote-btn {
  background: #28a745;
  color: white;
}

.deactivate-btn {
  background: #dc3545;
  color: white;
}

.promote-btn:hover,
.deactivate-btn:hover {
  opacity: 0.8;
}

/* 新增样式 */
.filters {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  align-items: center;
}

.filter-select,
.status-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  font-size: 14px;
}

.refresh-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.refresh-btn:hover {
  background: #218838;
}

.no-data {
  text-align: center;
  padding: 40px;
  color: #666;
  font-style: italic;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.app-details p {
  margin: 5px 0;
  font-size: 14px;
  color: #666;
}

.app-actions {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.update-btn,
.details-btn,
.delete-btn,
.upload-btn,
.complete-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  white-space: nowrap;
}

.update-btn {
  background: #007bff;
  color: white;
}

.details-btn {
  background: #6c757d;
  color: white;
}

.delete-btn {
  background: #dc3545;
  color: white;
}

.upload-btn {
  background: #28a745;
  color: white;
}

.complete-btn {
  background: #2196f3;
  color: white;
}

.update-btn:hover,
.details-btn:hover,
.delete-btn:hover,
.upload-btn:hover,
.complete-btn:hover {
  opacity: 0.8;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-top: 20px;
  padding: 20px 0;
}

.page-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
}

.page-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.page-info {
  color: #666;
  font-size: 14px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
}

.modal-body {
  padding: 30px;
}

.detail-section {
  margin-bottom: 25px;
}

.detail-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  border-bottom: 2px solid #007bff;
  padding-bottom: 5px;
}

.detail-section p {
  margin: 8px 0;
  color: #666;
  line-height: 1.5;
}

.file-item {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 10px;
}

.file-item p {
  margin: 5px 0;
}

/* 报告文件样式 */
.report-item {
  background: #f8f9fa;
  border-radius: 12px;
  margin-bottom: 15px;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

.report-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.report-header h5 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.report-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.report-status.free {
  background: #28a745;
  color: white;
}

.report-status.unpaid {
  background: #dc3545;
  color: white;
}

.report-status.paid {
  background: #28a745;
  color: white;
}

.report-details {
  padding: 20px;
}

.report-details p {
  margin: 8px 0;
  color: #555;
}

.download-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  margin-top: 10px;
}

.download-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.download-btn.preview {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.download-btn.full {
  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
  color: #333;
}

.modal-footer {
  padding: 20px 30px;
  border-top: 1px solid #eee;
  background: #f8f9fa;
}

.status-update {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.notes-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  min-width: 200px;
}

.file-upload {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.file-input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  min-width: 200px;
}

/* 增强的用户管理样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.create-btn {
  background: #10b981;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.create-btn:hover {
  background: #059669;
}

.user-item.enhanced {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  margin-bottom: 0.5rem;
  background: white;
  transition: box-shadow 0.2s;
}

.user-item.enhanced:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.user-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.user-name {
  font-size: 1.1rem;
  color: #1f2937;
}

.user-email {
  color: #6b7280;
  margin: 0.25rem 0;
}

.user-meta {
  display: flex;
  gap: 1rem;
  margin: 0.5rem 0;
}

.user-status {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.user-status.active {
  background: #d1fae5;
  color: #065f46;
}

.user-status.inactive {
  background: #fee2e2;
  color: #991b1b;
}

.user-org {
  color: #6b7280;
  font-size: 0.875rem;
}

.user-date {
  color: #9ca3af;
  font-size: 0.75rem;
  margin: 0.25rem 0 0 0;
}

.user-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: flex-start;
}

.action-btn {
  padding: 0.375rem 0.75rem;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.2s;
}

.view-btn {
  background: #e5e7eb;
  color: #374151;
}

.view-btn:hover {
  background: #d1d5db;
}

.edit-btn {
  background: #3b82f6;
  color: white;
}

.edit-btn:hover {
  background: #2563eb;
}

.activate-btn {
  background: #10b981;
  color: white;
}

.activate-btn:hover {
  background: #059669;
}

.reset-btn {
  background: #8b5cf6;
  color: white;
}

.reset-btn:hover {
  background: #7c3aed;
}

/* 用户表单样式 */
.user-form {
  max-width: 600px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 0.25rem;
  font-weight: 500;
  color: #374151;
}

.form-input,
.form-select {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.cancel-btn {
  background: #6b7280;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  cursor: pointer;
}

.cancel-btn:hover {
  background: #4b5563;
}

.submit-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  cursor: pointer;
}

.submit-btn:hover {
  background: #2563eb;
}

/* 申请完成相关样式 */
.complete-application {
  border-top: 1px solid #e5e7eb;
  padding-top: 1rem;
  margin-top: 1rem;
}

.complete-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.file-upload-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.file-upload-group label {
  font-weight: 500;
  color: #374151;
}

.notes-textarea {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  resize: vertical;
}

.notes-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.complete-btn {
  background: #10b981;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.complete-btn:hover {
  background: #059669;
}

/* 报告文件样式 */
.report-file {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  margin-bottom: 0.5rem;
  background: #f8fafc;
}

.file-info {
  flex: 1;
}

.download-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.download-btn:hover {
  background: #2563eb;
}

/* 状态更新样式优化 */
.status-update {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  flex-wrap: wrap;
}

.status-select {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background: white;
}

.notes-input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  min-width: 200px;
}

.update-btn {
  background: #f59e0b;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  cursor: pointer;
  font-weight: 500;
}

.update-btn:hover {
  background: #d97706;
}

.upload-btn {
  background: #8b5cf6;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  cursor: pointer;
  font-weight: 500;
}

.upload-btn:hover {
  background: #7c3aed;
}
</style>

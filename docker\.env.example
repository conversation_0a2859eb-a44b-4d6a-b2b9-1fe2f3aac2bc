# ============================================================================
# Quantix Docker Environment Configuration Example
# ============================================================================
# Copy this file to .env and update the values according to your environment

# ============================================================================
# Docker Compose Configuration
# ============================================================================
# Project name (used as prefix for container names)
COMPOSE_PROJECT_NAME=quantix

# ============================================================================
# MySQL Database Configuration
# ============================================================================
# MySQL root password
MYSQL_ROOT_PASSWORD=your_secure_root_password

# Application database
MYSQL_DATABASE=quantix
MYSQL_USER=quantix_user
MYSQL_PASSWORD=your_secure_database_password

# MySQL port mapping (host:container)
MYSQL_PORT=3306

# ============================================================================
# Backend Service Configuration
# ============================================================================
# Backend port mapping (host:container)
BACKEND_PORT=3001

# Environment for backend service
NODE_ENV=production

# ============================================================================
# Frontend Service Configuration
# ============================================================================
# Frontend port mapping (host:container)
FRONTEND_PORT=80

# ============================================================================
# Volume Configuration
# ============================================================================
# MySQL data volume
MYSQL_DATA_VOLUME=quantix_mysql_data

# Upload files volume
UPLOADS_VOLUME=quantix_uploads

# Logs volume
LOGS_VOLUME=quantix_logs

# ============================================================================
# Network Configuration
# ============================================================================
# Docker network name
NETWORK_NAME=quantix_network

# ============================================================================
# Security Configuration
# ============================================================================
# JWT secret for production
JWT_SECRET=your-production-jwt-secret-key-very-long-and-secure

# ============================================================================
# Email Configuration
# ============================================================================
# Email service configuration
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-production-app-password

# ============================================================================
# External Services
# ============================================================================
# OpenAI API key
OPENAI_API_KEY=your-production-openai-api-key

# Stripe keys
STRIPE_SECRET_KEY=sk_live_your_production_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_live_your_production_stripe_publishable_key

# ============================================================================
# Monitoring and Logging
# ============================================================================
# Log level
LOG_LEVEL=info

# Enable metrics
ENABLE_METRICS=true

import { createI18n } from 'vue-i18n'

// 从localStorage获取保存的语言设置，如果没有则使用默认的中文
const getStoredLocale = (): string => {
  const stored = localStorage.getItem('app-locale')
  return stored || 'zh'
}

// 保存语言设置到localStorage
export const setStoredLocale = (locale: string): void => {
  localStorage.setItem('app-locale', locale)
}

const i18n = createI18n({
  legacy: false,
  locale: getStoredLocale(),
  fallbackLocale: 'zh',
  messages: {
    en: {
      welcome: 'Protein Complex Analysis Platform',
      login: 'Login',
      register: 'Register',
      dashboard: 'Dashboard',
      report: 'Report',
      chat: 'Chat',
      captcha: 'Captcha',
      home: 'Home',
      features: 'Features',
      aboutUs: 'About Us',
      contactUs: 'Contact Us',
      heroTitle: 'Precise Analysis, Unveiling the Mysteries of Life',
      heroSubtitle:
        'Providing leading protein complex analysis services to help your scientific research breakthroughs.',
      startAnalysis: 'Start Analysis Now',
      coreFeatures: 'Core Features',
      highThroughput: 'High-Throughput Data Processing',
      highThroughputDesc:
        'Supports various raw genetic data formats, rapidly processing large-scale datasets.',
      smartAlgorithm: 'Intelligent Analysis Algorithm',
      smartAlgorithmDesc:
        'Utilizes advanced AI algorithms to provide precise protein interaction and structure prediction.',
      visualReport: 'Visual Reports',
      visualReportDesc:
        'Generates intuitive and easy-to-understand analysis reports, supporting multi-dimensional data visualization.',
      qaTitle: 'Professional Q&A for Complex Protein Analysis',
      qaSubtitle:
        'Encountering protein analysis challenges? Our AI intelligent Q&A system provides professional answers to help you overcome research challenges.',
      askNow: 'Ask Now',
      aboutUsDesc:
        'We are committed to providing high-quality protein complex analysis solutions for researchers worldwide. Our team consists of experienced bioinformatics experts and software engineers, ensuring the professionalism and reliability of our services.',
      phone: 'Phone',
      email: 'Email',
      address: 'Address',
      addressDetail: '123 Street, City, Province, China',
      allRightsReserved: 'All rights reserved.',
      loginWelcomeMessage:
        'Welcome to our protein complex analysis service. Please log in to submit your testing requirements.',
      password: 'Password',
      noAccount: 'No account yet?',
      registerNow: 'Register now',
      confirmPassword: 'Confirm Password',
      haveAccount: 'Already have an account?',
      loginNow: 'Login now',
      contactCustomerService: 'Contact Customer Service',
      logout: 'Logout',
      myApplications: 'My Applications',
      submitNewApplication: 'Submit New Application',
      myAnalysisApplications: 'My Analysis Applications',
      noApplicationsYet: 'You have not submitted any analysis applications yet.',
      submitNewApplicationNow: 'Submit New Application Now',
      applicationNumber: 'Application Number',
      inProgress: 'In Progress',
      completed: 'Completed',
      submissionDate: 'Submission Date',
      contactPhone: 'Contact Phone',
      geneDataFile: 'Gene Data File',
      viewReportPreview: 'View Report Preview',
      submitNewAnalysisApplication: 'Submit New Analysis Application',
      originalGeneDataFile: 'Original Gene Data File',
      fileSelected: 'File Selected',
      submitApplication: 'Submit Application',
      reportPreview: 'Analysis Report Preview',
      backToDashboard: 'Back to Dashboard',
      reportSummary: 'Report Summary',
      reportSummaryDesc:
        'This is a summary of the protein complex analysis report. The report details the analysis of genetic data and provides key structural and functional predictions.',
      analysisResultsOverview: 'Analysis Results Overview',
      proteinInteractionNetworkAnalysis: 'Protein Interaction Network Analysis',
      complexStructurePrediction: 'Complex Structure Prediction',
      functionalEnrichmentAnalysis: 'Functional Enrichment Analysis',
      potentialDrugTargetIdentification: 'Potential Drug Target Identification',
      fullReportPrompt: 'To view the full report, please pay to download.',
      payToDownloadFullReport: 'Pay to Download Full Report',
      back: 'Back',
      enterYourQuestion: 'Enter your question...',
      send: 'Send',
      helloAiAssistant:
        'Hello! I am your protein complex analysis AI assistant. How can I help you?',
      complexQuestionResponse:
        "Regarding the question '{question}' this is a complex area that requires in-depth analysis.",
      aiAssistantError: 'Sorry, the AI assistant is temporarily unable to answer your question.',
      // Admin and system related
      adminDashboard: 'Admin Dashboard',
      adminLogin: 'Admin Login',
      adminPlatform: 'Quantix Platform Admin',
      username: 'Username',
      loggingIn: 'Logging in...',
      registering: 'Registering...',
      loading: 'Loading...',
      totalUsers: 'Total Users',
      totalApplications: 'Total Applications',
      totalReports: 'Total Reports',
      systemInit: 'System Initialization',
      welcomeToQuantix: 'Welcome to Quantix Protein Analysis Platform',
      setupAdminAccount: 'Please set up your super admin account',
      adminUsername: 'Admin Username *',
      usernameRules: 'Username can only contain letters, numbers, underscores and hyphens',
      verificationCode: 'Verification Code',
      enterVerificationCode: 'Please enter verification code',
      verificationCodeSent: 'We have sent the verification code to your email, please check.',
      submitVerification: 'Submit Verification',
      passwordRequirements:
        'Password must be at least 6 characters with uppercase, lowercase and numbers',
      analysisType: 'Analysis Type',
      free: 'Free',
      fileName: 'File Name',
      analysisReport: 'Analysis Report',
      previewReport: 'Preview Report',
      chinese: 'Chinese',
      japanese: 'Japanese',
      // Admin dashboard specific (avoiding duplicates)
      processing: 'Processing',
      userManagement: 'User Management',
      manageUsers: 'Manage Users',
      applicationManagement: 'Application Management',
      manageApplications: 'Manage Applications',
      systemStatus: 'System Status',
      status: 'Status',
      healthy: 'Healthy',
      uptime: 'Uptime',
      memoryUsage: 'Memory Usage',
      checkSystem: 'Check System',
      allRoles: 'All Roles',
      normalUser: 'Normal User',
      administrator: 'Administrator',
      superAdmin: 'Super Administrator',
      allStatuses: 'All Statuses',
      active: 'Active',
      inactive: 'Inactive',
      refresh: 'Refresh',
      noUsersFound: 'No users found',
      viewDetails: 'View Details',
      edit: 'Edit',
      activate: 'Activate',
      deactivate: 'Deactivate',
      resetPassword: 'Reset Password',
      delete: 'Delete',
      previousPage: 'Previous Page',
      nextPage: 'Next Page',
      page: 'Page',
      of: 'of',
      pending: 'Pending',
      failed: 'Failed',
      cancelled: 'Cancelled',
      noApplicationsFound: 'No applications found',
      completeApplication: 'Complete Application',
      deleteApplication: 'Delete Application',
      applicationDetails: 'Application Details',
      basicInfo: 'Basic Information',
      estimatedCost: 'Estimated Cost',
      createdAt: 'Created At',
      updatedAt: 'Updated At',
      userInfo: 'User Information',
      organization: 'Organization',
      uploadedFiles: 'Uploaded Files',
      fileType: 'File Type',
      fileSize: 'File Size',
      analysisParameters: 'Analysis Parameters',
      algorithm: 'Algorithm',
      confidence: 'Confidence',
      maxIterations: 'Max Iterations',
      notes: 'Notes',
      reportFiles: 'Report Files',
      downloadPreviewReport: 'Download Preview Report',
      fullReport: 'Full Report',
      paidDownload: 'Paid Download',
      paymentStatus: 'Payment Status',
      downloadFullReport: 'Download Full Report',
      additionalFiles: 'Additional Files',
      uploadTime: 'Upload Time',
      createUser: 'Create User',
      firstName: 'First Name',
      lastName: 'Last Name',
      role: 'Role',
      create: 'Create',
      cancel: 'Cancel',
      editUser: 'Edit User',
      update: 'Update',
      userDetails: 'User Details',
      name: 'Name',
      emailVerified: 'Email Verified',
      verified: 'Verified',
      notVerified: 'Not Verified',
      // Unified Login Page
      checkingSystemStatus: 'Checking system status...',
      verifyingSystemInit: 'Verifying system initialization status, please wait',
      userLogin: 'User Login',
      adminOnlyAccess: '🔒 Admin access only',
      useAdminAccount: 'Use your admin account to login',
      pleaseUseAdminAccount: 'Please use admin account to login',
      usernameOrEmail: 'Username/Email',
      enterAdminUsernameOrEmail: 'Please enter admin username or email',
      enterAdminPassword: 'Please enter admin password',
      enterYourEmail: 'Please enter your email',
      enterYourPassword: 'Please enter your password',
      noAccountYet: 'No account yet? Register now',
      systemInitSuccess: 'System initialization successful! Please login with your account',
      pleaseEnterEmailAndPassword: 'Please enter email and password',
      pleaseEnterUsernameAndPassword: 'Please enter username and password',
      loginFailed: 'Login failed',
      networkError: 'Network error, please try again later',
      loginFailedCheckCredentials: 'Login failed, please check username and password',
      registrationTime: 'Registration Time',
      close: 'Close',
      previewReportRequired: 'Preview Report (Required) *',
      fullReportRequired: 'Full Report (Required) *',
      reportPrice: 'Report Price ($)',
      paymentStatusLabel: 'Payment Status',
      unpaid: 'Unpaid',
      paid: 'Paid',
      freeAccess: 'Free',
      completionNotes: 'Completion Notes',
      completeBtn: 'Complete',
      // Navigation
      backToHomepage: 'Back to Homepage',
      homepage: 'Homepage',

      // Console specific translations
      userConsole: 'User Console',
      loadingText: 'Loading...',
      progress: 'Progress',
      currentStep: 'Current Step',
      generationTime: 'Generation Time',
      payForDownload: 'Pay for Download',
      cancelApplication: 'Cancel Application',
      proteinComplexAnalysis: 'Protein Complex Analysis',
      proteinInteractionAnalysis: 'Protein Interaction Analysis',
      structurePrediction: 'Structure Prediction',
      drugTargetAnalysis: 'Drug Target Analysis',
      supportedFormats: 'Supported formats: FASTA, FASTQ, PDB, SDF, TXT (max 5 files)',
      removeFile: 'Remove',
      notesPlaceholder: 'Please enter any special requirements or instructions',
      submitting: 'Submitting...',

      // Payment specific translations
      paymentPage: 'Payment Page',
      goBack: 'Go Back',
      orderInfo: 'Order Information',
      productName: 'Product Name',
      orderAmount: 'Order Amount',
      orderNumber: 'Order Number',
      selectPaymentMethod: 'Select Payment Method',
      paymentDescription: 'You are paying for the full report fee for application',
      wechatPay: 'WeChat Pay',
      alipay: 'Alipay',
      generatePaymentCode: 'Generating payment code...',
      payNow: 'Pay Now',
      scanToPay: 'Scan to Pay',
      generatingQRCode: 'Generating QR code...',
      paymentAmount: 'Payment Amount',
      orderWillExpire: 'Order will expire in',
      paymentTips: 'Payment Tips',
      wechatScanTip: 'Please use WeChat to scan the QR code above to complete payment',
      alipayScanTip: 'Please use Alipay to scan the QR code above to complete payment',
      autoRedirectTip: 'The page will automatically redirect after payment is completed',
      contactServiceTip: 'If you encounter any problems, please contact customer service',
      checkPaymentStatus: 'Check Payment Status',
      checking: 'Checking...',
      cancelPayment: 'Cancel Payment',
      simulatePaymentSuccess: 'Simulate Payment Success',
      paymentSuccess: 'Payment Successful!',
      paymentCompleted: 'Your payment has been completed, you can now download the full report',
      goToDownload: 'Go to Download',
      paymentFailed: 'Payment Failed',
      paymentError: 'There was a problem during payment, please try again',
      retryPayment: 'Retry Payment',
      paymentCancelled: 'Payment Cancelled',
      paymentCancelledDesc: 'You have cancelled this payment',
      paymentExpired: 'Payment Expired',
      paymentExpiredDesc: 'The payment order has expired, please create a new one',
      expired: 'Expired',
    },
    zh: {
      welcome: '蛋白质复合物分析平台',
      login: '登录',
      register: '注册',
      dashboard: '仪表盘',
      report: '报告',
      chat: '聊天',
      captcha: '验证码',
      home: '首页',
      features: '功能',
      aboutUs: '关于我们',
      contactUs: '联系我们',
      heroTitle: '精准分析，洞察生命奥秘',
      heroSubtitle: '提供领先的蛋白质复合物分析服务，助力您的科研突破。',
      startAnalysis: '立即开始分析',
      coreFeatures: '核心功能',
      highThroughput: '高通量数据处理',
      highThroughputDesc: '支持多种原始基因数据格式，快速处理大规模数据集。',
      smartAlgorithm: '智能分析算法',
      smartAlgorithmDesc: '采用先进的AI算法，提供精准的蛋白质相互作用和结构预测。',
      visualReport: '可视化报告',
      visualReportDesc: '生成直观易懂的分析报告，支持多维度数据可视化。',
      qaTitle: '复合蛋白质分析专业问答',
      qaSubtitle: '遇到蛋白质分析难题？我们的AI智能问答系统为您提供专业解答，助您攻克科研挑战。',
      askNow: '立即提问',
      aboutUsDesc:
        '我们致力于为全球科研人员提供高质量的蛋白质复合物分析解决方案。我们的团队由经验丰富的生物信息学专家和软件工程师组成，确保服务的专业性和可靠性。',
      phone: '电话',
      email: '邮箱',
      address: '地址',
      addressDetail: '中国某市某区某街道123号',
      allRightsReserved: '保留所有权利。',
      loginWelcomeMessage: '欢迎使用我们的蛋白质复合物分析服务。请登录以提交您的检测需求。',
      password: '密码',
      noAccount: '还没有账号？',
      registerNow: '立即注册',
      confirmPassword: '确认密码',
      haveAccount: '已有账号？',
      loginNow: '立即登录',
      contactCustomerService: '联系客服',
      logout: '退出登录',
      myApplications: '我的申请',
      submitNewApplication: '提交新申请',
      myAnalysisApplications: '我的分析申请',
      noApplicationsYet: '您还没有提交任何分析申请。',
      submitNewApplicationNow: '立即提交新申请',
      applicationNumber: '申请编号',
      inProgress: '进行中',
      completed: '已完成',
      submissionDate: '提交日期',
      contactPhone: '联系电话',
      geneDataFile: '基因数据文件',
      viewReportPreview: '查看报告预览',
      submitNewAnalysisApplication: '提交新的分析申请',
      originalGeneDataFile: '原始基因数据文件',
      fileSelected: '已选择文件',
      submitApplication: '提交申请',
      reportPreview: '分析报告预览',
      backToDashboard: '返回仪表盘',
      reportSummary: '报告摘要',
      reportSummaryDesc:
        '这是一份关于蛋白质复合物分析的报告摘要。报告详细分析了基因数据，并提供了关键的结构和功能预测。',
      analysisResultsOverview: '分析结果概览',
      proteinInteractionNetworkAnalysis: '蛋白质相互作用网络分析',
      complexStructurePrediction: '复合物结构预测',
      functionalEnrichmentAnalysis: '功能富集分析',
      potentialDrugTargetIdentification: '潜在药物靶点识别',
      fullReportPrompt: '如需查看完整报告，请付费下载。',
      payToDownloadFullReport: '付费下载完整报告',
      back: '返回',
      enterYourQuestion: '请输入您的问题...',
      send: '发送',
      helloAiAssistant: '您好！我是复合蛋白质分析AI助手，有什么可以帮助您的吗？',
      complexQuestionResponse: '关于“{question}”的问题，这是一个复杂的领域，需要深入分析。',
      aiAssistantError: '抱歉，AI助手暂时无法回答您的问题。',
      // Admin and system related
      adminDashboard: '🛠️ 管理员仪表板',
      adminLogin: '🔐 管理员登录',
      adminPlatform: 'Quantix 平台管理后台',
      username: '用户名',
      loggingIn: '登录中...',
      registering: '注册中...',
      loading: '加载中...',
      totalUsers: '总用户数',
      totalApplications: '总申请数',
      totalReports: '总报告数',
      systemInit: '🚀 系统初始化',
      welcomeToQuantix: '欢迎使用 Quantix 蛋白质分析平台',
      setupAdminAccount: '请设置您的超级管理员账户',
      adminUsername: '管理员用户名 *',
      usernameRules: '用户名只能包含字母、数字、下划线和连字符',
      verificationCode: '验证码',
      enterVerificationCode: '请输入验证码',
      verificationCodeSent: '我们已将验证码发送到您的邮箱，请查收。',
      submitVerification: '提交验证',
      passwordRequirements: '密码至少6位，包含大小写字母和数字',
      analysisType: '分析类型',
      free: '免费',
      fileName: '文件名',
      analysisReport: '📊 分析报告',
      previewReport: '📄 预览报告',
      chinese: '中文',
      japanese: '日本語',
      // Admin dashboard specific (avoiding duplicates)
      processing: '处理中',
      userManagement: '👥 用户管理',
      manageUsers: '管理用户',
      applicationManagement: '📋 申请管理',
      manageApplications: '管理申请',
      systemStatus: '🔧 系统状态',
      status: '状态',
      healthy: '正常',
      uptime: '运行时间',
      memoryUsage: '内存使用',
      checkSystem: '检查系统',
      allRoles: '所有角色',
      normalUser: '普通用户',
      administrator: '管理员',
      superAdmin: '超级管理员',
      allStatuses: '所有状态',
      active: '活跃',
      inactive: '停用',
      refresh: '🔄 刷新',
      noUsersFound: '没有找到用户记录',
      viewDetails: '查看详情',
      edit: '编辑',
      activate: '激活',
      deactivate: '停用',
      resetPassword: '重置密码',
      delete: '删除',
      previousPage: '上一页',
      nextPage: '下一页',
      page: '第',
      of: '页，共',
      pending: '待处理',
      failed: '失败',
      cancelled: '已取消',
      noApplicationsFound: '没有找到申请记录',
      completeApplication: '完成申请',
      deleteApplication: '删除',
      applicationDetails: '申请详情',
      basicInfo: '基本信息',
      estimatedCost: '预估费用',
      createdAt: '创建时间',
      updatedAt: '更新时间',
      userInfo: '用户信息',
      organization: '机构',
      uploadedFiles: '上传文件',
      fileType: '类型',
      fileSize: '大小',
      analysisParameters: '分析参数',
      algorithm: '算法',
      confidence: '置信度',
      maxIterations: '最大迭代',
      notes: '备注',
      reportFiles: '报告文件',
      downloadPreviewReport: '下载预览报告',
      fullReport: '完整报告',
      paidDownload: '付费下载',
      paymentStatus: '付费状态',
      downloadFullReport: '下载完整报告',
      additionalFiles: '附加文件',
      uploadTime: '上传时间',
      createUser: '创建用户',
      firstName: '名',
      lastName: '姓',
      role: '角色',
      create: '创建',
      cancel: '取消',
      editUser: '编辑用户',
      update: '更新',
      userDetails: '用户详情',
      name: '姓名',
      emailVerified: '邮箱验证',
      verified: '已验证',
      notVerified: '未验证',
      registrationTime: '注册时间',
      // Unified Login Page
      checkingSystemStatus: '检查系统状态...',
      verifyingSystemInit: '正在验证系统初始化状态，请稍候',
      userLogin: '👤 用户登录',
      adminOnlyAccess: '🔒 仅限系统管理员访问',
      useAdminAccount: '使用您设置的管理员账户登录',
      pleaseUseAdminAccount: '请使用管理员账户登录',
      usernameOrEmail: '用户名/邮箱',
      enterAdminUsernameOrEmail: '请输入管理员用户名或邮箱',
      enterAdminPassword: '请输入管理员密码',
      enterYourEmail: '请输入您的邮箱',
      enterYourPassword: '请输入您的密码',
      noAccountYet: '还没有账户？立即注册',
      systemInitSuccess: '系统初始化成功！请使用您设置的账户登录',
      pleaseEnterEmailAndPassword: '请填写邮箱和密码',
      pleaseEnterUsernameAndPassword: '请填写用户名和密码',
      loginFailed: '登录失败',
      networkError: '网络错误，请稍后重试',
      loginFailedCheckCredentials: '登录失败，请检查用户名和密码',
      close: '关闭',
      previewReportRequired: '预览报告 (必需) *',
      fullReportRequired: '完整报告 (必需) *',
      reportPrice: '报告价格 ($)',
      paymentStatusLabel: '付费状态',
      unpaid: '未付费',
      paid: '已付费',
      freeAccess: '免费',
      completionNotes: '完成备注',
      completeBtn: '完成',
      // Navigation
      backToHomepage: '返回首页',
      homepage: '首页',

      // Console specific translations
      userConsole: '用户控制台',
      loadingText: '加载中...',
      progress: '进度',
      currentStep: '当前步骤',
      generationTime: '生成时间',
      payForDownload: '付费下载',
      cancelApplication: '取消申请',
      proteinComplexAnalysis: '蛋白质复合物分析',
      proteinInteractionAnalysis: '蛋白质相互作用分析',
      structurePrediction: '结构预测',
      drugTargetAnalysis: '药物靶点分析',
      supportedFormats: '支持格式：FASTA、FASTQ、PDB、SDF、TXT（最多5个文件）',
      removeFile: '移除',
      notesPlaceholder: '请输入任何特殊要求或说明',
      submitting: '提交中...',

      // Payment specific translations
      paymentPage: '支付页面',
      goBack: '返回',
      orderInfo: '订单信息',
      productName: '产品名称',
      orderAmount: '订单金额',
      orderNumber: '订单号',
      selectPaymentMethod: '选择支付方式',
      paymentDescription: '您正在为申请支付完整报告费用',
      wechatPay: '微信支付',
      alipay: '支付宝',
      generatePaymentCode: '生成支付码...',
      payNow: '立即支付',
      scanToPay: '扫码支付',
      generatingQRCode: '生成二维码...',
      paymentAmount: '支付金额',
      orderWillExpire: '订单将在以下时间过期',
      paymentTips: '支付提示',
      wechatScanTip: '请使用微信扫描上方二维码完成支付',
      alipayScanTip: '请使用支付宝扫描上方二维码完成支付',
      autoRedirectTip: '支付完成后页面将自动跳转',
      contactServiceTip: '如遇到任何问题，请联系客服',
      checkPaymentStatus: '检查支付状态',
      checking: '检查中...',
      cancelPayment: '取消支付',
      simulatePaymentSuccess: '模拟支付成功',
      paymentSuccess: '支付成功！',
      paymentCompleted: '您的支付已完成，现在可以下载完整报告',
      goToDownload: '去下载',
      paymentFailed: '支付失败',
      paymentError: '支付过程中出现问题，请重试',
      retryPayment: '重试支付',
      paymentCancelled: '支付已取消',
      paymentCancelledDesc: '您已取消此次支付',
      paymentExpired: '支付已过期',
      paymentExpiredDesc: '支付订单已过期，请创建新的订单',
      expired: '已过期',
    },
    es: {
      welcome: 'Plataforma de Análisis de Complejos Proteicos',
      login: 'Iniciar Sesión',
      register: 'Registrarse',
      dashboard: 'Panel de Control',
      report: 'Informe',
      chat: 'Chat',
      captcha: 'Captcha',
      home: 'Inicio',
      features: 'Características',
      aboutUs: 'Sobre Nosotros',
      contactUs: 'Contacto',
      heroTitle: 'Análisis Preciso, Desvelando los Misterios de la Vida',
      heroSubtitle:
        'Ofreciendo servicios líderes en análisis de complejos proteicos para ayudar a sus avances en investigación científica.',
      startAnalysis: 'Comenzar Análisis Ahora',
      coreFeatures: 'Características Principales',
      highThroughput: 'Procesamiento de Datos de Alto Rendimiento',
      highThroughputDesc:
        'Soporta varios formatos de datos genéticos crudos, procesando rápidamente grandes conjuntos de datos.',
      smartAlgorithm: 'Algoritmo de Análisis Inteligente',
      smartAlgorithmDesc:
        'Utiliza algoritmos avanzados de IA para proporcionar predicciones precisas de interacción y estructura de proteínas.',
      visualReport: 'Informes Visuales',
      visualReportDesc:
        'Genera informes de análisis intuitivos y fáciles de entender, soportando la visualización de datos multidimensional.',
      qaTitle: 'Preguntas y Respuestas Profesionales para el Análisis de Proteínas Complejas',
      qaSubtitle:
        '¿Encuentra desafíos en el análisis de proteínas? Nuestro sistema de preguntas y respuestas inteligente de IA le proporciona respuestas profesionales para ayudarle a superar los desafíos de investigación.',
      askNow: 'Preguntar Ahora',
      aboutUsDesc:
        'Estamos comprometidos a proporcionar soluciones de análisis de complejos proteicos de alta calidad para investigadores de todo el mundo. Nuestro equipo está formado por expertos en bioinformática e ingenieros de software experimentados, lo que garantiza la profesionalidad y fiabilidad de nuestros servicios.',
      phone: 'Teléfono',
      email: 'Correo Electrónico',
      address: 'Dirección',
      addressDetail: '123 Calle, Ciudad, Provincia, España',
      allRightsReserved: 'Todos los derechos reservados.',
      loginWelcomeMessage:
        'Bienvenido a nuestro servicio de análisis de complejos proteicos. Por favor, inicie sesión para enviar sus requisitos de prueba.',
      password: 'Contraseña',
      noAccount: '¿Aún no tienes cuenta?',
      registerNow: 'Registrarse ahora',
      confirmPassword: 'Confirmar Contraseña',
      haveAccount: '¿Ya tienes una cuenta?',
      loginNow: 'Iniciar sesión ahora',
      contactCustomerService: 'Contactar con el Servicio al Cliente',
      logout: 'Cerrar Sesión',
      myApplications: 'Mis Aplicaciones',
      submitNewApplication: 'Enviar Nueva Aplicación',
      myAnalysisApplications: 'Mis Aplicaciones de Análisis',
      noApplicationsYet: 'Aún no ha enviado ninguna aplicación de análisis.',
      submitNewApplicationNow: 'Enviar Nueva Aplicación Ahora',
      applicationNumber: 'Número de Aplicación',
      inProgress: 'En Progreso',
      completed: 'Completado',
      submissionDate: 'Fecha de Envío',
      contactPhone: 'Teléfono de Contacto',
      geneDataFile: 'Archivo de Datos Genéticos',
      viewReportPreview: 'Ver Vista Previa del Informe',
      submitNewAnalysisApplication: 'Enviar Nueva Aplicación de Análisis',
      originalGeneDataFile: 'Archivo de Datos Genéticos Original',
      fileSelected: 'Archivo Seleccionado',
      submitApplication: 'Enviar Aplicación',
      reportPreview: 'Vista Previa del Informe de Análisis',
      backToDashboard: 'Volver al Panel de Control',
      reportSummary: 'Resumen del Informe',
      reportSummaryDesc:
        'Este es un resumen del informe de análisis de complejos proteicos. El informe detalla el análisis de datos genéticos y proporciona predicciones clave de estructura y función.',
      analysisResultsOverview: 'Resumen de Resultados del Análisis',
      proteinInteractionNetworkAnalysis: 'Análisis de Red de Interacción Proteica',
      complexStructurePrediction: 'Predicción de Estructura Compleja',
      functionalEnrichmentAnalysis: 'Análisis de Enriquecimiento Funcional',
      potentialDrugTargetIdentification: 'Identificación de Posibles Dianas Farmacológicas',
      fullReportPrompt: 'Para ver el informe completo, pague para descargarlo.',
      payToDownloadFullReport: 'Pagar para Descargar Informe Completo',
      back: 'Volver',
      enterYourQuestion: 'Ingrese su pregunta...',
      send: 'Enviar',
      helloAiAssistant:
        '¡Hola! Soy su asistente de IA para el análisis de complejos proteicos. ¿En qué puedo ayudarle?',
      complexQuestionResponse:
        "Respecto a la pregunta '{question}', esta es un área compleja que requiere un análisis en profundidad.",
      aiAssistantError:
        'Lo siento, el asistente de IA no puede responder a su pregunta temporalmente.',
      // Unified Login Page
      checkingSystemStatus: 'Verificando estado del sistema...',
      verifyingSystemInit: 'Verificando el estado de inicialización del sistema, por favor espere',
      userLogin: '👤 Inicio de Sesión de Usuario',
      adminLogin: '🔧 Inicio de Sesión de Administrador',
      adminOnlyAccess: '🔒 Solo acceso de administrador',
      useAdminAccount: 'Use su cuenta de administrador para iniciar sesión',
      pleaseUseAdminAccount: 'Por favor use la cuenta de administrador para iniciar sesión',
      usernameOrEmail: 'Nombre de Usuario/Correo',
      enterAdminUsernameOrEmail: 'Por favor ingrese el nombre de usuario o correo del administrador',
      enterAdminPassword: 'Por favor ingrese la contraseña del administrador',
      enterYourEmail: 'Por favor ingrese su correo electrónico',
      enterYourPassword: 'Por favor ingrese su contraseña',
      noAccountYet: '¿Aún no tienes cuenta? Regístrate ahora',
      systemInitSuccess: '¡Inicialización del sistema exitosa! Por favor inicie sesión con su cuenta',
      pleaseEnterEmailAndPassword: 'Por favor ingrese correo y contraseña',
      pleaseEnterUsernameAndPassword: 'Por favor ingrese nombre de usuario y contraseña',
      loginFailed: 'Error de inicio de sesión',
      networkError: 'Error de red, por favor intente más tarde',
      loginFailedCheckCredentials: 'Error de inicio de sesión, por favor verifique el nombre de usuario y contraseña',
      // Navigation
      backToHomepage: 'Volver a la Página Principal',
      homepage: 'Página Principal',
    },
    fr: {
      welcome: "Plateforme d'Analyse de Complexes Protéiques",
      login: 'Connexion',
      register: "S'inscrire",
      dashboard: 'Tableau de Bord',
      report: 'Rapport',
      chat: 'Chat',
      captcha: 'Captcha',
      home: 'Accueil',
      features: 'Fonctionnalités',
      aboutUs: 'À Propos de Nous',
      contactUs: 'Contactez-nous',
      heroTitle: 'Analyse Précise, Dévoilant les Mystères de la Vie',
      heroSubtitle:
        'Offrant des services de pointe en analyse de complexes protéiques pour soutenir vos avancées en recherche scientifique.',
      startAnalysis: "Commencer l'Analyse Maintenant",
      coreFeatures: 'Fonctionnalités Principales',
      highThroughput: 'Traitement de Données à Haut Débit',
      highThroughputDesc:
        'Prend en charge divers formats de données génétiques brutes, traitant rapidement de grands ensembles de données.',
      smartAlgorithm: "Algorithme d'Analyse Intelligent",
      smartAlgorithmDesc:
        "Utilise des algorithmes d'IA avancés pour fournir des prédictions précises d'interaction et de structure des protéines.",
      visualReport: 'Rapports Visuels',
      visualReportDesc:
        "Génère des rapports d'analyse intuitifs et faciles à comprendre, prenant en charge la visualisation de données multidimensionnelles.",
      qaTitle: "Questions-Réponses Professionnelles pour l'Analyse de Protéines Complexes",
      qaSubtitle:
        "Vous rencontrez des défis dans l'analyse des protéines ? Notre système de questions-réponses intelligent par IA vous fournit des réponses professionnelles pour vous aider à surmonter les défis de recherche.",
      askNow: 'Poser une Question Maintenant',
      aboutUsDesc:
        "Nous nous engageons à fournir des solutions d'analyse de complexes protéiques de haute qualité aux chercheurs du monde entier. Notre équipe est composée d'experts en bioinformatique et d'ingénieurs logiciels expérimentés, garantissant le professionnalisme et la fiabilité de nos services.",
      phone: 'Téléphone',
      email: 'Courriel',
      address: 'Adresse',
      addressDetail: '123 Rue, Ville, Province, France',
      allRightsReserved: 'Tous droits réservés.',
      loginWelcomeMessage:
        "Bienvenue à notre service d'analyse de complexes protéiques. Veuillez vous connecter pour soumettre vos exigences de test.",
      password: 'Mot de Passe',
      noAccount: 'Pas encore de compte ?',
      registerNow: "S'inscrire maintenant",
      confirmPassword: 'Confirmer le Mot de Passe',
      haveAccount: 'Vous avez déjà un compte ?',
      loginNow: 'Se connecter maintenant',
      contactCustomerService: 'Contacter le Service Client',
      logout: 'Déconnexion',
      myApplications: 'Mes Applications',
      submitNewApplication: 'Soumettre une Nouvelle Application',
      myAnalysisApplications: "Mes Applications d'Analyse",
      noApplicationsYet: "Vous n'avez pas encore soumis d'applications d'analyse.",
      submitNewApplicationNow: 'Soumettre une Nouvelle Application Maintenant',
      applicationNumber: "Numéro d'Application",
      inProgress: 'En Cours',
      completed: 'Terminé',
      submissionDate: 'Date de Soumission',
      contactPhone: 'Téléphone de Contact',
      geneDataFile: 'Fichier de Données Génétiques',
      viewReportPreview: "Voir l'Aperçu du Rapport",
      submitNewAnalysisApplication: "Soumettre une Nouvelle Application d'Analyse",
      originalGeneDataFile: 'Fichier de Données Génétiques Original',
      fileSelected: 'Fichier Sélectionné',
      submitApplication: "Soumettre l'Application",
      reportPreview: "Aperçu du Rapport d'Analyse",
      backToDashboard: 'Retour au Tableau de Bord',
      reportSummary: 'Résumé du Rapport',
      reportSummaryDesc:
        "Ceci est un résumé du rapport d'analyse de complexes protéiques. Le rapport détaille l'analyse des données génétiques et fournit des prédictions clés de structure et de fonction.",
      analysisResultsOverview: "Aperçu des Résultats de l'Analyse",
      proteinInteractionNetworkAnalysis: "Analyse du Réseau d'Interaction Protéique",
      complexStructurePrediction: 'Prédiction de Structure Complexe',
      functionalEnrichmentAnalysis: "Analyse d'Enrichissement Fonctionnel",
      potentialDrugTargetIdentification: 'Identification de Cibles Médicamenteuses Potentielles',
      fullReportPrompt: 'Pour consulter le rapport complet, veuillez payer pour le télécharger.',
      payToDownloadFullReport: 'Payer pour Télécharger le Rapport Complet',
      back: 'Retour',
      enterYourQuestion: 'Entrez votre question...',
      send: 'Envoyer',
      helloAiAssistant:
        "Bonjour ! Je suis votre assistant IA pour l'analyse de complexes protéiques. Comment puis-je vous aider ?",
      complexQuestionResponse:
        "Concernant la question '{question}', c\'est un domaine complexe qui nécessite une analyse approfondie.",
      aiAssistantError:
        "Désolé, l'assistant IA est temporairement incapable de répondre à votre question.",
      // Unified Login Page
      checkingSystemStatus: 'Vérification du statut du système...',
      verifyingSystemInit: 'Vérification du statut d\'initialisation du système, veuillez patienter',
      userLogin: '👤 Connexion Utilisateur',
      adminLogin: '🔧 Connexion Administrateur',
      adminOnlyAccess: '🔒 Accès administrateur uniquement',
      useAdminAccount: 'Utilisez votre compte administrateur pour vous connecter',
      pleaseUseAdminAccount: 'Veuillez utiliser le compte administrateur pour vous connecter',
      usernameOrEmail: 'Nom d\'utilisateur/Courriel',
      enterAdminUsernameOrEmail: 'Veuillez saisir le nom d\'utilisateur ou courriel administrateur',
      enterAdminPassword: 'Veuillez saisir le mot de passe administrateur',
      enterYourEmail: 'Veuillez saisir votre courriel',
      enterYourPassword: 'Veuillez saisir votre mot de passe',
      noAccountYet: 'Pas encore de compte ? Inscrivez-vous maintenant',
      systemInitSuccess: 'Initialisation du système réussie ! Veuillez vous connecter avec votre compte',
      pleaseEnterEmailAndPassword: 'Veuillez saisir courriel et mot de passe',
      pleaseEnterUsernameAndPassword: 'Veuillez saisir nom d\'utilisateur et mot de passe',
      loginFailed: 'Échec de connexion',
      networkError: 'Erreur réseau, veuillez réessayer plus tard',
      loginFailedCheckCredentials: 'Échec de connexion, veuillez vérifier le nom d\'utilisateur et mot de passe',
      // Navigation
      backToHomepage: 'Retour à la Page d\'Accueil',
      homepage: 'Page d\'Accueil',
    },
    de: {
      welcome: 'Plattform für Proteinkomplexanalyse',
      login: 'Anmelden',
      register: 'Registrieren',
      dashboard: 'Dashboard',
      report: 'Bericht',
      chat: 'Chat',
      captcha: 'Captcha',
      home: 'Startseite',
      features: 'Funktionen',
      aboutUs: 'Über Uns',
      contactUs: 'Kontakt',
      heroTitle: 'Präzise Analyse, Entschlüsselung der Geheimnisse des Lebens',
      heroSubtitle:
        'Wir bieten führende Dienstleistungen zur Proteinkomplexanalyse, um Ihre wissenschaftlichen Durchbrüche zu unterstützen.',
      startAnalysis: 'Analyse jetzt starten',
      coreFeatures: 'Kernfunktionen',
      highThroughput: 'Hochdurchsatz-Datenverarbeitung',
      highThroughputDesc:
        'Unterstützt verschiedene Rohdatenformate für genetische Daten und verarbeitet schnell große Datensätze.',
      smartAlgorithm: 'Intelligenter Analysealgorithmus',
      smartAlgorithmDesc:
        'Verwendet fortschrittliche KI-Algorithmen, um präzise Vorhersagen zur Proteininteraktion und -struktur zu liefern.',
      visualReport: 'Visuelle Berichte',
      visualReportDesc:
        'Erstellt intuitive und leicht verständliche Analyseberichte, die eine mehrdimensionale Datenvisualisierung unterstützen.',
      qaTitle: 'Professionelle Fragen und Antworten zur komplexen Proteinanalyse',
      qaSubtitle:
        'Haben Sie Herausforderungen bei der Proteinanalyse? Unser intelligentes KI-Frage-Antwort-System bietet Ihnen professionelle Antworten, um Ihnen bei der Bewältigung von Forschungsherausforderungen zu helfen.',
      askNow: 'Jetzt fragen',
      aboutUsDesc:
        'Wir sind bestrebt, Forschern weltweit hochwertige Lösungen zur Proteinkomplexanalyse anzubieten. Unser Team besteht aus erfahrenen Bioinformatik-Experten und Software-Ingenieuren, die die Professionalität und Zuverlässigkeit unserer Dienstleistungen gewährleisten.',
      phone: 'Telefon',
      email: 'E-Mail',
      address: 'Adresse',
      addressDetail: '123 Straße, Stadt, Bundesland, Deutschland',
      allRightsReserved: 'Alle Rechte vorbehalten.',
      loginWelcomeMessage:
        'Willkommen bei unserem Proteinkomplexanalyse-Service. Bitte melden Sie sich an, um Ihre Testanforderungen einzureichen.',
      password: 'Passwort',
      noAccount: 'Noch kein Konto?',
      registerNow: 'Jetzt registrieren',
      confirmPassword: 'Passwort bestätigen',
      haveAccount: 'Haben Sie bereits ein Konto?',
      loginNow: 'Jetzt anmelden',
      contactCustomerService: 'Kundendienst kontaktieren',
      logout: 'Abmelden',
      myApplications: 'Meine Anwendungen',
      submitNewApplication: 'Neue Anwendung einreichen',
      myAnalysisApplications: 'Meine Analyseanwendungen',
      noApplicationsYet: 'Sie haben noch keine Analyseanwendungen eingereicht.',
      submitNewApplicationNow: 'Jetzt neue Anwendung einreichen',
      applicationNumber: 'Antragsnummer',
      inProgress: 'In Bearbeitung',
      completed: 'Abgeschlossen',
      submissionDate: 'Einreichungsdatum',
      contactPhone: 'Kontakttelefon',
      geneDataFile: 'Gendaten-Datei',
      viewReportPreview: 'Berichtsvorschau anzeigen',
      submitNewAnalysisApplication: 'Neue Analyseanwendung einreichen',
      originalGeneDataFile: 'Original-Gendaten-Datei',
      fileSelected: 'Datei ausgewählt',
      submitApplication: 'Anwendung einreichen',
      reportPreview: 'Analyseberichtsvorschau',
      backToDashboard: 'Zurück zum Dashboard',
      reportSummary: 'Berichtszusammenfassung',
      reportSummaryDesc:
        'Dies ist eine Zusammenfassung des Berichts zur Proteinkomplexanalyse. Der Bericht analysiert detailliert die Gendaten und liefert wichtige Vorhersagen zu Struktur und Funktion.',
      analysisResultsOverview: 'Übersicht der Analyseergebnisse',
      proteinInteractionNetworkAnalysis: 'Analyse des Proteininteraktionsnetzwerks',
      complexStructurePrediction: 'Komplexe Strukturvorhersage',
      functionalEnrichmentAnalysis: 'Funktionelle Anreicherungsanalyse',
      potentialDrugTargetIdentification: 'Identifizierung potenzieller Medikamentenziele',
      fullReportPrompt:
        'Um den vollständigen Bericht anzuzeigen, zahlen Sie bitte für den Download.',
      payToDownloadFullReport: 'Vollständigen Bericht herunterladen',
      back: 'Zurück',
      enterYourQuestion: 'Geben Sie Ihre Frage ein...',
      send: 'Senden',
      helloAiAssistant:
        'Hallo! Ich bin Ihr KI-Assistent für die Proteinkomplexanalyse. Wie kann ich Ihnen helfen?',
      complexQuestionResponse:
        "Bezüglich der Frage '{question}' ist dies ein komplexes Gebiet, das eine eingehende Analyse erfordert.",
      aiAssistantError:
        'Entschuldigung, der KI-Assistent kann Ihre Frage vorübergehend nicht beantworten.',
      // Unified Login Page
      checkingSystemStatus: 'Systemstatus überprüfen...',
      verifyingSystemInit: 'Überprüfung des Systeminitialisierungsstatus, bitte warten',
      userLogin: '👤 Benutzer-Anmeldung',
      adminLogin: '🔧 Administrator-Anmeldung',
      adminOnlyAccess: '🔒 Nur Administrator-Zugang',
      useAdminAccount: 'Verwenden Sie Ihr Administrator-Konto zum Anmelden',
      pleaseUseAdminAccount: 'Bitte verwenden Sie das Administrator-Konto zum Anmelden',
      usernameOrEmail: 'Benutzername/E-Mail',
      enterAdminUsernameOrEmail: 'Bitte geben Sie Administrator-Benutzername oder E-Mail ein',
      enterAdminPassword: 'Bitte geben Sie das Administrator-Passwort ein',
      enterYourEmail: 'Bitte geben Sie Ihre E-Mail ein',
      enterYourPassword: 'Bitte geben Sie Ihr Passwort ein',
      noAccountYet: 'Noch kein Konto? Jetzt registrieren',
      systemInitSuccess: 'Systeminitialisierung erfolgreich! Bitte melden Sie sich mit Ihrem Konto an',
      pleaseEnterEmailAndPassword: 'Bitte geben Sie E-Mail und Passwort ein',
      pleaseEnterUsernameAndPassword: 'Bitte geben Sie Benutzername und Passwort ein',
      loginFailed: 'Anmeldung fehlgeschlagen',
      networkError: 'Netzwerkfehler, bitte versuchen Sie es später erneut',
      loginFailedCheckCredentials: 'Anmeldung fehlgeschlagen, bitte überprüfen Sie Benutzername und Passwort',
      // Navigation
      backToHomepage: 'Zur Startseite zurückkehren',
      homepage: 'Startseite',
    },
    ja: {
      welcome: 'タンパク質複合体解析プラットフォーム',
      login: 'ログイン',
      register: '登録',
      dashboard: 'ダッシュボード',
      report: 'レポート',
      chat: 'チャット',
      captcha: 'キャプチャ',
      home: 'ホーム',
      features: '機能',
      aboutUs: '私たちについて',
      contactUs: 'お問い合わせ',
      heroTitle: '精密な分析で生命の謎を解き明かす',
      heroSubtitle:
        '科学研究の進歩を支援するため、最先端のタンパク質複合体解析サービスを提供します。',
      startAnalysis: '今すぐ分析を開始',
      coreFeatures: '主要機能',
      highThroughput: 'ハイスループットデータ処理',
      highThroughputDesc:
        '様々な生遺伝子データ形式をサポートし、大規模なデータセットを迅速に処理します。',
      smartAlgorithm: 'スマート分析アルゴリズム',
      smartAlgorithmDesc:
        '高度なAIアルゴリズムを使用して、タンパク質の相互作用と構造の正確な予測を提供します。',
      visualReport: 'ビジュアルレポート',
      visualReportDesc:
        '直感的で理解しやすい分析レポートを生成し、多次元データ視覚化をサポートします。',
      qaTitle: '複合タンパク質分析に関する専門的なQ&A',
      qaSubtitle:
        'タンパク質分析で課題に直面していますか？当社のAIスマートQ&Aシステムは、研究の課題を克服するのに役立つ専門的な回答を提供します。',
      askNow: '今すぐ質問',
      aboutUsDesc:
        '私たちは、世界中の研究者に高品質なタンパク質複合体解析ソリューションを提供することに尽力しています。私たちのチームは、経験豊富なバイオインフォマティクス専門家とソフトウェアエンジニアで構成されており、サービスの専門性と信頼性を保証します。',
      phone: '電話',
      email: 'メール',
      address: '住所',
      addressDetail: '日本、東京都、千代田区、1-2-3',
      allRightsReserved: '全著作権所有。',
      loginWelcomeMessage:
        'タンパク質複合体解析サービスへようこそ。テスト要件を提出するにはログインしてください。',
      password: 'パスワード',
      noAccount: 'アカウントをお持ちではありませんか？',
      registerNow: '今すぐ登録',
      confirmPassword: 'パスワードを確認',
      haveAccount: 'すでにアカウントをお持ちですか？',
      loginNow: '今すぐログイン',
      contactCustomerService: 'カスタマーサービスに連絡',
      logout: 'ログアウト',
      myApplications: '私のアプリケーション',
      submitNewApplication: '新しいアプリケーションを提出',
      myAnalysisApplications: '私の分析アプリケーション',
      noApplicationsYet: 'まだ分析アプリケーションを提出していません。',
      submitNewApplicationNow: '今すぐ新しいアプリケーションを提出',
      applicationNumber: '申請番号',
      inProgress: '進行中',
      completed: '完了',
      submissionDate: '提出日',
      contactPhone: '連絡先電話番号',
      geneDataFile: '遺伝子データファイル',
      viewReportPreview: 'レポートプレビューを表示',
      submitNewAnalysisApplication: '新しい分析アプリケーションを提出',
      originalGeneDataFile: '元の遺伝子データファイル',
      fileSelected: 'ファイルが選択されました',
      submitApplication: 'アプリケーションを提出',
      reportPreview: '分析レポートプレビュー',
      backToDashboard: 'ダッシュボードに戻る',
      reportSummary: 'レポート概要',
      reportSummaryDesc:
        'これはタンパク質複合体解析レポートの概要です。レポートは遺伝子データを詳細に分析し、構造と機能に関する重要な予測を提供します。',
      analysisResultsOverview: '分析結果の概要',
      proteinInteractionNetworkAnalysis: 'タンパク質相互作用ネットワーク分析',
      complexStructurePrediction: '複合体構造予測',
      functionalEnrichmentAnalysis: '機能濃縮分析',
      potentialDrugTargetIdentification: '潜在的な薬剤標的の特定',
      fullReportPrompt: '完全なレポートを表示するには、ダウンロードのために支払いをしてください。',
      payToDownloadFullReport: '完全なレポートをダウンロードするために支払う',
      back: '戻る',
      enterYourQuestion: '質問を入力してください...',
      send: '送信',
      helloAiAssistant:
        'こんにちは！私は複合タンパク質分析AIアシスタントです。何かお手伝いできますか？',
      complexQuestionResponse: "'{question}'に関する質問は、詳細な分析が必要な複雑な分野です。",
      aiAssistantError: '申し訳ありませんが、AIアシスタントは一時的にご質問にお答えできません。',
      // Unified Login Page
      checkingSystemStatus: 'システム状態を確認中...',
      verifyingSystemInit: 'システム初期化状態を確認中です。しばらくお待ちください',
      userLogin: '👤 ユーザーログイン',
      adminLogin: '🔧 管理者ログイン',
      adminOnlyAccess: '🔒 管理者のみアクセス可能',
      useAdminAccount: '設定した管理者アカウントでログインしてください',
      pleaseUseAdminAccount: '管理者アカウントでログインしてください',
      usernameOrEmail: 'ユーザー名/メール',
      enterAdminUsernameOrEmail: '管理者ユーザー名またはメールアドレスを入力してください',
      enterAdminPassword: '管理者パスワードを入力してください',
      enterYourEmail: 'メールアドレスを入力してください',
      enterYourPassword: 'パスワードを入力してください',
      noAccountYet: 'アカウントをお持ちでない方は今すぐ登録',
      systemInitSuccess: 'システム初期化が成功しました！設定したアカウントでログインしてください',
      pleaseEnterEmailAndPassword: 'メールアドレスとパスワードを入力してください',
      pleaseEnterUsernameAndPassword: 'ユーザー名とパスワードを入力してください',
      loginFailed: 'ログインに失敗しました',
      networkError: 'ネットワークエラーです。後でもう一度お試しください',
      loginFailedCheckCredentials: 'ログインに失敗しました。ユーザー名とパスワードを確認してください',
      // Navigation
      backToHomepage: 'ホームページに戻る',
      homepage: 'ホームページ',
    },
    ko: {
      welcome: '단백질 복합체 분석 플랫폼',
      login: '로그인',
      register: '회원가입',
      dashboard: '대시보드',
      report: '보고서',
      chat: '채팅',
      captcha: '캡차',
      home: '홈',
      features: '기능',
      aboutUs: '회사 소개',
      contactUs: '문의하기',
      heroTitle: '정밀 분석, 생명의 신비 해독',
      heroSubtitle:
        '과학 연구의 발전을 지원하기 위해 선도적인 단백질 복합체 분석 서비스를 제공합니다.',
      startAnalysis: '지금 분석 시작',
      coreFeatures: '핵심 기능',
      highThroughput: '고처리량 데이터 처리',
      highThroughputDesc:
        '다양한 원시 유전자 데이터 형식을 지원하며 대규모 데이터 세트를 신속하게 처리합니다.',
      smartAlgorithm: '스마트 분석 알고리즘',
      smartAlgorithmDesc:
        '고급 AI 알고리즘을 사용하여 단백질 상호 작용 및 구조에 대한 정확한 예측을 제공합니다.',
      visualReport: '시각적 보고서',
      visualReportDesc:
        '직관적이고 이해하기 쉬운 분석 보고서를 생성하며 다차원 데이터 시각화를 지원합니다.',
      qaTitle: '복합 단백질 분석 전문 Q&A',
      qaSubtitle:
        '단백질 분석에 어려움을 겪고 계십니까? 당사의 AI 스마트 Q&A 시스템은 연구 과제를 극복하는 데 도움이 되는 전문적인 답변을 제공합니다.',
      askNow: '지금 질문하기',
      aboutUsDesc:
        '우리는 전 세계 연구원들에게 고품질 단백질 복합체 분석 솔루션을 제공하기 위해 최선을 다하고 있습니다. 우리 팀은 숙련된 생물 정보학 전문가와 소프트웨어 엔지니어로 구성되어 서비스의 전문성과 신뢰성을 보장합니다.',
      phone: '전화',
      email: '이메일',
      address: '주소',
      addressDetail: '대한민국 서울특별시 강남구 123번지',
      allRightsReserved: '모든 권리 보유.',
      loginWelcomeMessage:
        '단백질 복합체 분석 서비스에 오신 것을 환영합니다. 테스트 요구 사항을 제출하려면 로그인하십시오.',
      password: '비밀번호',
      noAccount: '아직 계정이 없으신가요?',
      registerNow: '지금 등록',
      confirmPassword: '비밀번호 확인',
      haveAccount: '이미 계정이 있으신가요?',
      loginNow: '지금 로그인',
      contactCustomerService: '고객 서비스 문의',
      logout: '로그아웃',
      myApplications: '내 신청',
      submitNewApplication: '새 신청 제출',
      myAnalysisApplications: '내 분석 신청',
      noApplicationsYet: '아직 분석 신청을 제출하지 않았습니다.',
      submitNewApplicationNow: '지금 새 신청 제출',
      applicationNumber: '신청 번호',
      inProgress: '진행 중',
      completed: '완료',
      submissionDate: '제출일',
      contactPhone: '연락처 전화번호',
      geneDataFile: '유전자 데이터 파일',
      viewReportPreview: '보고서 미리보기',
      submitNewAnalysisApplication: '새 분석 신청 제출',
      originalGeneDataFile: '원본 유전자 데이터 파일',
      fileSelected: '파일 선택됨',
      submitApplication: '신청 제출',
      reportPreview: '분석 보고서 미리보기',
      backToDashboard: '대시보드로 돌아가기',
      reportSummary: '보고서 요약',
      reportSummaryDesc:
        '이것은 단백질 복합체 분석 보고서의 요약입니다. 보고서는 유전자 데이터를 자세히 분석하고 구조 및 기능에 대한 주요 예측을 제공합니다.',
      analysisResultsOverview: '분석 결과 개요',
      proteinInteractionNetworkAnalysis: '단백질 상호 작용 네트워크 분석',
      complexStructurePrediction: '복합체 구조 예측',
      functionalEnrichmentAnalysis: '기능 농축 분석',
      potentialDrugTargetIdentification: '잠재적 약물 표적 식별',
      fullReportPrompt: '전체 보고서를 보려면 다운로드 비용을 지불하십시오.',
      payToDownloadFullReport: '전체 보고서 다운로드 비용 지불',
      back: '뒤로',
      enterYourQuestion: '질문을 입력하세요...',
      send: '보내기',
      helloAiAssistant:
        '안녕하세요! 저는 복합 단백질 분석 AI 어시스턴트입니다. 무엇을 도와드릴까요?',
      complexQuestionResponse: "'{question}'에 대한 질문은 심층 분석이 필요한 복잡한 분야입니다.",
      aiAssistantError: '죄송합니다. AI 어시스턴트가 일시적으로 질문에 답변할 수 없습니다.',
      // Unified Login Page
      checkingSystemStatus: '시스템 상태 확인 중...',
      verifyingSystemInit: '시스템 초기화 상태를 확인 중입니다. 잠시 기다려 주세요',
      userLogin: '👤 사용자 로그인',
      adminLogin: '🔧 관리자 로그인',
      adminOnlyAccess: '🔒 관리자만 접근 가능',
      useAdminAccount: '설정한 관리자 계정으로 로그인하세요',
      pleaseUseAdminAccount: '관리자 계정으로 로그인해 주세요',
      usernameOrEmail: '사용자명/이메일',
      enterAdminUsernameOrEmail: '관리자 사용자명 또는 이메일을 입력해 주세요',
      enterAdminPassword: '관리자 비밀번호를 입력해 주세요',
      enterYourEmail: '이메일을 입력해 주세요',
      enterYourPassword: '비밀번호를 입력해 주세요',
      noAccountYet: '아직 계정이 없으신가요? 지금 등록하세요',
      systemInitSuccess: '시스템 초기화가 성공했습니다! 설정한 계정으로 로그인해 주세요',
      pleaseEnterEmailAndPassword: '이메일과 비밀번호를 입력해 주세요',
      pleaseEnterUsernameAndPassword: '사용자명과 비밀번호를 입력해 주세요',
      loginFailed: '로그인 실패',
      networkError: '네트워크 오류입니다. 나중에 다시 시도해 주세요',
      loginFailedCheckCredentials: '로그인 실패, 사용자명과 비밀번호를 확인해 주세요',
      // Navigation
      backToHomepage: '홈페이지로 돌아가기',
      homepage: '홈페이지',
    },
  },
})
export default i18n

const { sequelize } = require('../models');
const { CompatibilityChecker } = require('../utils/compatibility');

describe('Upgrade Compatibility Tests', () => {
  let originalTables = [];
  let testDatabase;

  beforeAll(async () => {
    // 记录原始表结构
    const queryInterface = sequelize.getQueryInterface();
    originalTables = await queryInterface.showAllTables();
  });

  afterAll(async () => {
    // 确保测试后数据库状态正确
    await sequelize.close();
  });

  describe('Compatibility Checker', () => {
    it('should detect current system as upgraded', async () => {
      const checker = new CompatibilityChecker();
      const result = await checker.check();

      expect(result.compatible).toBe(true);
      expect(result.issues).toHaveLength(0);
      expect(result.autoFixed).toBe(false);
    });

    it('should verify all required payment fields exist', async () => {
      const queryInterface = sequelize.getQueryInterface();
      const applicationColumns = await queryInterface.describeTable('applications');

      const requiredPaymentFields = [
        'paymentOrderId',
        'paymentMethod', 
        'paymentStatus',
        'thirdPartyOrderId',
        'thirdPartyTransactionId',
        'qrCodeUrl',
        'qrCodeContent',
        'paidAt',
        'paymentExpiresAt',
        'paymentCallbackData',
        'paymentFailureReason',
        'refundStatus',
        'refundAmount',
        'refundedAt',
        'paymentClientIp',
        'paymentUserAgent'
      ];

      for (const field of requiredPaymentFields) {
        expect(applicationColumns).toHaveProperty(field);
      }
    });

    it('should confirm payments table does not exist', async () => {
      const queryInterface = sequelize.getQueryInterface();
      const tables = await queryInterface.showAllTables();
      
      expect(tables).not.toContain('payments');
    });
  });

  describe('System Status Detection', () => {
    it('should correctly identify upgraded system', async () => {
      const checker = new CompatibilityChecker();

      // checkSystemStatus 是私有方法，我们通过 check() 方法来验证
      const result = await checker.check();

      expect(result.compatible).toBe(true);
      expect(result.issues).toHaveLength(0);
    });
  });

  describe('Backward Compatibility', () => {
    it('should handle applications without payment data gracefully', async () => {
      const { Application, User } = require('../models');

      // 创建测试用户
      const testUser = await User.create({
        email: `test-compat-${Date.now()}@example.com`,
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
        isEmailVerified: true
      });

      // 创建没有支付信息的申请
      const application = await Application.create({
        userId: testUser.id,
        contactPhone: '13800138000',
        contactEmail: testUser.email,
        files: [{
          originalName: 'test.pdb',
          filename: 'test.pdb',
          path: '/test/path',
          size: 1024,
          mimetype: 'application/octet-stream'
        }],
        analysisType: 'protein_complex',
        status: 'completed',
        reportPrice: 99.99,
        reportPaymentStatus: 'unpaid'
        // 注意：没有设置任何支付相关字段
      });

      // 验证应用可以正常创建和查询
      expect(application.id).toBeDefined();
      expect(application.paymentOrderId).toBeUndefined();
      expect(application.paymentStatus).toBeUndefined();

      // 验证支付相关方法可以正常工作
      expect(application.canBePaid()).toBe(false);
      expect(application.isPaymentExpired()).toBeFalsy();

      // 清理测试数据
      await Application.destroy({ where: { id: application.id } });
      await User.destroy({ where: { id: testUser.id } });
    });

    it('should support payment initialization on existing applications', async () => {
      const { Application, User } = require('../models');

      // 创建测试用户
      const testUser = await User.create({
        email: `test-payment-init-${Date.now()}@example.com`,
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
        isEmailVerified: true
      });

      // 创建申请
      const application = await Application.create({
        userId: testUser.id,
        contactPhone: '13800138000',
        contactEmail: testUser.email,
        files: [{ originalName: 'test.pdb' }],
        analysisType: 'protein_complex',
        status: 'completed',
        reportPrice: 99.99,
        reportPaymentStatus: 'unpaid'
      });

      // 初始化支付
      application.initializePayment('wechat', '127.0.0.1', 'test-agent');
      await application.save();

      // 验证支付信息已正确设置
      expect(application.paymentOrderId).toBeTruthy();
      expect(application.paymentMethod).toBe('wechat');
      expect(application.paymentStatus).toBe('pending');
      expect(application.paymentExpiresAt).toBeTruthy();
      expect(application.paymentClientIp).toBe('127.0.0.1');

      // 验证支付方法工作正常
      expect(application.canBePaid()).toBe(true);
      expect(application.isPaymentExpired()).toBe(false);

      // 清理测试数据
      await Application.destroy({ where: { id: application.id } });
      await User.destroy({ where: { id: testUser.id } });
    });
  });

  describe('Data Migration Simulation', () => {
    it('should handle payment status mapping correctly', async () => {
      const { Application } = require('../models');

      // 模拟从老版本迁移的数据映射
      const statusMappings = [
        { oldStatus: 'pending', newStatus: 'pending' },
        { oldStatus: 'paid', newStatus: 'paid' },
        { oldStatus: 'failed', newStatus: 'failed' },
        { oldStatus: 'cancelled', newStatus: 'cancelled' },
        { oldStatus: 'expired', newStatus: 'expired' }
      ];

      for (const mapping of statusMappings) {
        // 验证状态映射是否正确
        expect(['pending', 'paid', 'failed', 'cancelled', 'expired'])
          .toContain(mapping.newStatus);
      }
    });

    it('should validate payment method enum values', async () => {
      const { Application } = require('../models');
      
      // 验证支付方式枚举值
      const validPaymentMethods = ['wechat', 'alipay'];
      
      for (const method of validPaymentMethods) {
        // 这些值应该在模型定义中是有效的
        expect(['wechat', 'alipay']).toContain(method);
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors gracefully', async () => {
      // 模拟数据库连接错误
      const checker = new CompatibilityChecker();
      
      // 这个测试验证错误处理逻辑存在
      expect(checker.addIssue).toBeDefined();
      expect(typeof checker.addIssue).toBe('function');
    });

    it('should provide meaningful error messages', async () => {
      const checker = new CompatibilityChecker();
      
      // 添加测试问题
      checker.addIssue('error', '测试错误', '这是一个测试错误描述');
      
      expect(checker.issues).toHaveLength(1);
      expect(checker.issues[0].type).toBe('error');
      expect(checker.issues[0].title).toBe('测试错误');
      expect(checker.issues[0].description).toBe('这是一个测试错误描述');
    });
  });

  describe('Performance Impact', () => {
    it('should complete compatibility check quickly', async () => {
      const startTime = Date.now();
      
      const checker = new CompatibilityChecker();
      await checker.check();
      
      const duration = Date.now() - startTime;
      
      // 兼容性检查应该在合理时间内完成（< 5秒）
      expect(duration).toBeLessThan(5000);
    });
  });
});

# API 文档

## 基础信息

- **Base URL**: `http://localhost:3001/api`
- **认证方式**: <PERSON><PERSON> (JWT)
- **内容类型**: `application/json`

## 认证相关 API

### 用户注册
```http
POST /auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Password123",
  "confirmPassword": "Password123",
  "language": "zh"
}
```

**响应**:
```json
{
  "message": "User registered successfully. Please check your email for verification code.",
  "userId": "user_id",
  "email": "<EMAIL>"
}
```

### 邮箱验证
```http
POST /auth/verify-email
Content-Type: application/json

{
  "email": "<EMAIL>",
  "code": "123456"
}
```

**响应**:
```json
{
  "message": "Email verified successfully",
  "token": "jwt_token",
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "isEmailVerified": true
  }
}
```

### 用户登录
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Password123"
}
```

**响应**:
```json
{
  "message": "Login successful",
  "token": "jwt_token",
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "isEmailVerified": true,
    "profile": {},
    "preferences": {},
    "subscription": {}
  }
}
```

### 获取当前用户信息
```http
GET /auth/me
Authorization: Bearer jwt_token
```

**响应**:
```json
{
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "isEmailVerified": true,
    "profile": {},
    "preferences": {},
    "subscription": {}
  }
}
```

## 文件管理 API

### 上传文件
```http
POST /upload/files
Authorization: Bearer jwt_token
Content-Type: multipart/form-data

files: [File1, File2, ...]
```

**响应**:
```json
{
  "message": "Successfully uploaded 2 file(s)",
  "files": [
    {
      "originalName": "gene_data.fasta",
      "filename": "uuid_filename.fasta",
      "size": 1024,
      "fileType": "fasta",
      "uploadedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

### 获取文件列表
```http
GET /upload/files
Authorization: Bearer jwt_token
```

**响应**:
```json
{
  "files": [
    {
      "filename": "uuid_filename.fasta",
      "size": 1024,
      "fileType": "fasta",
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "totalCount": 1,
  "totalSize": 1024
}
```

### 下载文件
```http
GET /upload/files/:filename/download
Authorization: Bearer jwt_token
```

## 分析申请 API

### 提交分析申请
```http
POST /applications
Authorization: Bearer jwt_token
Content-Type: application/json

{
  "contactInfo": {
    "phone": "+1234567890"
  },
  "files": [
    {
      "originalName": "gene_data.fasta",
      "filename": "uuid_filename.fasta",
      "path": "/path/to/file",
      "size": 1024,
      "mimetype": "text/plain",
      "fileType": "fasta"
    }
  ],
  "analysisType": "protein_complex",
  "parameters": {
    "algorithm": "alphafold",
    "confidence": 0.7
  },
  "notes": "Special requirements"
}
```

**响应**:
```json
{
  "message": "Application submitted successfully",
  "application": {
    "id": "app_id",
    "applicationId": "APP001",
    "status": "pending",
    "analysisType": "protein_complex",
    "estimatedCost": 50,
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### 获取申请列表
```http
GET /applications?page=1&limit=10&status=pending
Authorization: Bearer jwt_token
```

**响应**:
```json
{
  "applications": [
    {
      "id": "app_id",
      "applicationId": "APP001",
      "status": "pending",
      "analysisType": "protein_complex",
      "progress": {
        "percentage": 0,
        "currentStep": "Queued"
      },
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 1,
    "totalCount": 1,
    "hasNext": false,
    "hasPrev": false
  }
}
```

### 获取申请详情
```http
GET /applications/:id
Authorization: Bearer jwt_token
```

**响应**:
```json
{
  "application": {
    "id": "app_id",
    "applicationId": "APP001",
    "status": "completed",
    "analysisType": "protein_complex",
    "parameters": {},
    "progress": {},
    "results": {},
    "files": [],
    "report": {}
  }
}
```

## 报告管理 API

### 根据申请ID获取报告
```http
GET /reports/application/:applicationId
Authorization: Bearer jwt_token
```

**响应**:
```json
{
  "report": {
    "id": "report_id",
    "reportId": "RPT001",
    "title": "Protein Complex Analysis Report",
    "summary": "Analysis summary...",
    "analysisResults": {},
    "visualizations": [],
    "access": {
      "isPaid": true,
      "price": 29.99
    },
    "status": "ready"
  }
}
```

### 下载报告
```http
GET /reports/:reportId/download?format=pdf
Authorization: Bearer jwt_token
```

## AI问答 API

### 发送消息（认证用户）
```http
POST /chat/message
Authorization: Bearer jwt_token
Content-Type: application/json

{
  "message": "什么是蛋白质复合物？",
  "language": "zh",
  "context": "用户正在查看分析结果"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "question": "什么是蛋白质复合物？",
    "answer": "蛋白质复合物是由两个或多个蛋白质亚基通过非共价键结合形成的功能性分子聚合体...",
    "language": "zh",
    "source": "openai",
    "confidence": 0.8,
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

### 公开消息（限制更严格）
```http
POST /chat/public/message
Content-Type: application/json

{
  "message": "What are protein complexes?",
  "language": "en"
}
```

### 获取建议问题
```http
GET /chat/suggestions?language=zh
```

**响应**:
```json
{
  "suggestions": [
    "什么是蛋白质复合物？",
    "如何分析蛋白质相互作用？",
    "AlphaFold在蛋白质结构预测中的应用是什么？"
  ],
  "language": "zh"
}
```

## 用户管理 API

### 更新用户资料
```http
PATCH /users/profile
Authorization: Bearer jwt_token
Content-Type: application/json

{
  "profile": {
    "firstName": "张",
    "lastName": "三",
    "organization": "某大学",
    "phone": "+86-13800138000",
    "country": "中国"
  }
}
```

### 更新用户偏好
```http
PATCH /users/preferences
Authorization: Bearer jwt_token
Content-Type: application/json

{
  "preferences": {
    "language": "zh",
    "notifications": {
      "email": true,
      "analysis": true
    }
  }
}
```

### 修改密码
```http
PATCH /users/password
Authorization: Bearer jwt_token
Content-Type: application/json

{
  "currentPassword": "OldPassword123",
  "newPassword": "NewPassword123",
  "confirmPassword": "NewPassword123"
}
```

### 获取用户统计
```http
GET /users/stats
Authorization: Bearer jwt_token
```

**响应**:
```json
{
  "stats": {
    "applications": {
      "total": 5,
      "pending": 1,
      "processing": 1,
      "completed": 3,
      "totalCost": 150
    },
    "reports": {
      "total": 3,
      "ready": 3
    },
    "account": {
      "memberSince": "2024-01-01T00:00:00.000Z",
      "subscription": {}
    }
  }
}
```

## 错误响应格式

所有API错误都遵循统一格式：

```json
{
  "error": "Error Type",
  "message": "Human readable error message",
  "details": [] // 可选，验证错误详情
}
```

## 状态码

- `200` - 成功
- `201` - 创建成功
- `400` - 请求错误
- `401` - 未认证
- `403` - 权限不足
- `404` - 资源不存在
- `409` - 冲突（如邮箱已存在）
- `429` - 请求过于频繁
- `500` - 服务器错误

## 速率限制

- 一般API：每15分钟100次请求
- 认证用户聊天：每15分钟20次请求
- 公开聊天：每小时5次请求

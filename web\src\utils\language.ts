// 语言设置工具函数
import { useI18n } from 'vue-i18n'
import { setStoredLocale } from '@/locales/i18n'

// 语言切换组合函数
export function useLanguage() {
  const { locale } = useI18n()

  // 切换语言并保存到localStorage
  const changeLanguage = (newLocale: string) => {
    locale.value = newLocale
    setStoredLocale(newLocale)
  }

  // 获取当前语言
  const getCurrentLanguage = () => {
    return locale.value
  }

  // 从localStorage初始化语言设置
  const initializeLanguage = () => {
    const storedLocale = localStorage.getItem('app-locale')
    if (storedLocale && storedLocale !== locale.value) {
      locale.value = storedLocale
    }
  }

  return {
    locale,
    changeLanguage,
    getCurrentLanguage,
    initializeLanguage
  }
}

// 支持的语言列表
export const supportedLanguages = [
  { code: 'en', name: 'English' },
  { code: 'zh', name: '中文' },
  { code: 'es', name: 'Espa<PERSON><PERSON>' },
  { code: 'fr', name: 'Fran<PERSON>' },
  { code: 'de', name: '<PERSON><PERSON><PERSON>' },
  { code: 'ja', name: '日本語' },
  { code: 'ko', name: '한국어' }
]

# 🧪 Tests 目录

这个目录包含了 Quantix 项目的各种测试文件，用于验证系统功能和调试问题。

## 📋 测试文件列表

### 🔄 端到端测试

#### `test-e2e-initialization.cjs`
**用途**: 完整的系统初始化端到端测试  
**覆盖功能**:
- 系统初始状态检查
- 系统初始化流程
- 管理员账户创建
- 登录功能验证
- 重复初始化防护
- 数据库状态验证

```bash
node tests/test-e2e-initialization.cjs
```

**预期结果**: 100% 测试通过率

### 🔐 登录测试

#### `test-login-api.cjs`
**用途**: 测试登录 API 功能  
**测试内容**:
- 管理员登录验证
- 密码哈希验证
- JWT 令牌生成
- 错误处理

```bash
node tests/test-login-api.cjs
```

#### `test-frontend-login.cjs`
**用途**: 测试前端登录修复  
**测试内容**:
- 前端存储操作
- setToken 方法验证
- 用户状态管理
- 登录流程完整性

```bash
node tests/test-frontend-login.cjs
```

### 🚀 初始化测试

#### `test-init-process.cjs`
**用途**: 测试系统初始化过程  
**测试内容**:
- 初始化 API 调用
- 管理员用户创建
- 数据库写入验证
- 状态更新检查

```bash
node tests/test-init-process.cjs
```

### 🔍 调试工具

#### `debug-hash-detailed.cjs`
**用途**: 详细的密码哈希调试  
**调试内容**:
- 密码哈希生成
- bcrypt 验证过程
- 哈希格式分析
- 兼容性测试

```bash
node tests/debug-hash-detailed.cjs
```

#### `debug-password.cjs`
**用途**: 密码相关问题调试  
**调试内容**:
- 密码存储验证
- 哈希算法测试
- 比较函数调试

```bash
node tests/debug-password.cjs
```

## 🎯 测试分类

### ✅ 功能测试
- **系统初始化**: `test-e2e-initialization.cjs`
- **用户认证**: `test-login-api.cjs`
- **前端集成**: `test-frontend-login.cjs`

### 🔧 集成测试
- **端到端流程**: `test-e2e-initialization.cjs`
- **API 集成**: `test-login-api.cjs`
- **数据库集成**: `test-init-process.cjs`

### 🐛 调试工具
- **密码调试**: `debug-hash-detailed.cjs`
- **哈希调试**: `debug-password.cjs`

## 🚀 运行测试

### 运行所有测试
```bash
# 端到端测试 (推荐)
node tests/test-e2e-initialization.cjs

# 单独功能测试
node tests/test-login-api.cjs
node tests/test-frontend-login.cjs
node tests/test-init-process.cjs
```

### 调试问题
```bash
# 密码相关问题
node tests/debug-hash-detailed.cjs
node tests/debug-password.cjs
```

## 📊 测试报告

### 成功指标
- **端到端测试**: 100% 通过率 (7/7 测试)
- **登录测试**: API 响应正常，Token 生成成功
- **初始化测试**: 管理员创建成功，数据库状态正确

### 测试覆盖
- ✅ 系统初始化流程
- ✅ 用户认证机制
- ✅ 密码安全验证
- ✅ 数据库操作
- ✅ 前端状态管理
- ✅ 错误处理机制

## 🛠️ 测试环境

### 前置条件
1. **后端服务**: 确保后端在 http://localhost:3001 运行
2. **数据库**: SQLite 数据库可访问
3. **依赖**: 安装了 node-fetch 等测试依赖

### 环境检查
```bash
# 检查后端服务
curl http://localhost:3001/api/health

# 检查系统状态
curl http://localhost:3001/api/system/init-status
```

## 📝 编写新测试

### 测试文件模板
```javascript
// 测试文件: test-new-feature.cjs
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const BASE_URL = 'http://localhost:3001';

async function testNewFeature() {
  console.log('🧪 测试新功能');
  
  try {
    // 测试逻辑
    const response = await fetch(`${BASE_URL}/api/endpoint`);
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ 测试通过');
      return true;
    } else {
      console.log('❌ 测试失败');
      return false;
    }
  } catch (error) {
    console.error('💥 测试错误:', error.message);
    return false;
  }
}

testNewFeature().then(success => {
  process.exit(success ? 0 : 1);
});
```

### 调试工具模板
```javascript
// 调试工具: debug-feature.cjs
async function debugFeature() {
  console.log('🔍 调试功能');
  
  try {
    // 调试逻辑
    console.log('📋 当前状态:', status);
    console.log('🔧 配置信息:', config);
    
  } catch (error) {
    console.error('💥 调试错误:', error);
  }
}

debugFeature();
```

## 🔧 测试最佳实践

### 1. 测试隔离
- 每个测试独立运行
- 清理测试数据
- 避免测试间依赖

### 2. 错误处理
- 捕获所有异常
- 提供详细错误信息
- 优雅的失败处理

### 3. 日志输出
- 使用颜色区分状态
- 提供详细的执行信息
- 包含调试数据

### 4. 断言验证
- 验证响应状态
- 检查数据结构
- 确认业务逻辑

## 🐛 故障排除

### 常见问题
1. **连接错误**: 检查后端服务是否运行
2. **权限错误**: 确保有数据库写入权限
3. **依赖缺失**: 安装 node-fetch 等依赖
4. **端口冲突**: 确认端口 3001 可用

### 调试步骤
1. 运行 `debug-hash-detailed.cjs` 检查密码问题
2. 检查后端日志输出
3. 验证数据库状态
4. 确认 API 响应格式

## 📈 测试指标

### 当前状态
- **端到端测试**: ✅ 100% 通过
- **登录功能**: ✅ 正常工作
- **初始化流程**: ✅ 完全可用
- **密码验证**: ✅ 修复完成

### 覆盖率目标
- **API 端点**: 90%+
- **核心功能**: 100%
- **错误场景**: 80%+

---

**维护**: Quantix 开发团队  
**更新**: 2025-06-19

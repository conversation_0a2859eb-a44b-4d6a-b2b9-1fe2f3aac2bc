@echo off
setlocal enabledelayedexpansion

echo 🐳 启动 Quantix 平台 - Docker 模式
echo.

REM 检查Docker是否运行
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker 未安装或未运行
    echo    请先安装并启动 Docker Desktop
    echo    下载地址: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo ✅ Docker 已就绪

REM 检查环境变量文件
if not exist ".env" (
    echo 📝 创建环境变量文件...
    copy ".env.docker" ".env"
    echo.
    echo ⚠️  请编辑 .env 文件并设置以下必需的环境变量:
    echo    - EMAIL_USER: 您的邮箱地址
    echo    - EMAIL_PASS: 您的邮箱应用密码
    echo    - OPENAI_API_KEY: 您的 OpenAI API 密钥
    echo.
    echo 按任意键继续使用默认配置，或按 Ctrl+C 退出并编辑 .env 文件...
    pause > nul
)

echo 🏗️ 构建和启动容器...
docker-compose -f docker/docker-compose.yml down
docker-compose -f docker/docker-compose.yml build --no-cache
docker-compose -f docker/docker-compose.yml up -d

if %errorlevel% neq 0 (
    echo ❌ Docker 容器启动失败
    echo 查看错误日志: docker-compose -f docker/docker-compose.yml logs
    pause
    exit /b 1
)

echo ⏳ 等待服务启动...
timeout /t 30 /nobreak > nul

echo 🔍 检查服务状态...
docker-compose -f docker/docker-compose.yml ps

echo.
echo 🎉 Quantix 平台已启动！
echo.
echo 📊 前端访问地址: http://localhost
echo 🔧 后端API地址: http://localhost/api
echo 🔐 管理员登录: http://localhost/admin/login
echo 📚 API健康检查: http://localhost/api/health
echo.
echo 🔑 默认管理员账户:
echo    用户名: admin
echo    密码: fHadmin
echo    邮箱: <EMAIL>
echo.
echo 📋 Docker 管理命令:
echo    查看日志: docker-compose logs
echo    停止服务: docker-compose down
echo    重启服务: docker-compose restart
echo    查看状态: docker-compose ps
echo.

REM 等待MySQL完全启动
echo ⏳ 等待数据库初始化...
timeout /t 10 /nobreak > nul

echo 🗄️ 初始化数据库和管理员账户...
docker-compose exec backend node scripts/init-database.js

echo.
echo Press any key to open the application...
pause > nul

start http://localhost

echo.
echo ✅ 应用已在浏览器中打开！
echo.
echo 要停止所有服务，请运行: docker-compose down
pause

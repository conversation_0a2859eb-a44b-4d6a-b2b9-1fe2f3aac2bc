const express = require('express');
const { body, validationResult } = require('express-validator');
const { User } = require('../models');


const router = express.Router();

// Check if system is initialized (has any admin users)
router.get('/init-status', async (req, res) => {
  try {
    const adminCount = await User.count({
      where: {
        role: ['admin', 'superadmin']
      }
    });

    res.json({
      isInitialized: adminCount > 0,
      adminCount,
      message: adminCount > 0 ? 'System is initialized' : 'System needs initialization'
    });

  } catch (error) {
    console.error('Check init status error:', error);
    res.status(500).json({
      error: 'Failed to check system status',
      message: 'An error occurred while checking system initialization status'
    });
  }
});

// Initialize system with first admin user
router.post('/initialize', [
  body('username')
    .isLength({ min: 3, max: 50 })
    .withMessage('Username must be between 3 and 50 characters')
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Username can only contain letters, numbers, underscores, and hyphens'),
  body('email')
    .isEmail()
    .withMessage('Please provide a valid email address'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long'),
  body('firstName')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('First name must be between 1 and 100 characters'),
  body('lastName')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Last name must be between 1 and 100 characters')
], async (req, res) => {
  try {
    // Validate input
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    // Check if system is already initialized
    const adminCount = await User.count({
      where: {
        role: ['admin', 'superadmin']
      }
    });

    if (adminCount > 0) {
      return res.status(400).json({
        error: 'System already initialized',
        message: 'The system has already been initialized with an admin user'
      });
    }

    const { username, email, password, firstName, lastName } = req.body;

    // Check if email already exists
    const existingUser = await User.findOne({ where: { email } });
    if (existingUser) {
      return res.status(400).json({
        error: 'Email already exists',
        message: 'A user with this email address already exists'
      });
    }

    // Create super admin user (password will be hashed by User model beforeCreate hook)
    const adminUser = await User.create({
      email,
      password: password, // Let the model handle password hashing
      isEmailVerified: true,
      firstName: firstName || 'Super',
      lastName: lastName || 'Admin',
      organization: 'Quantix Platform',
      role: 'superadmin',
      subscriptionPlan: 'premium',
      language: 'zh'
    });

    // Remove password from response
    const userResponse = {
      id: adminUser.id,
      email: adminUser.email,
      firstName: adminUser.firstName,
      lastName: adminUser.lastName,
      role: adminUser.role,
      createdAt: adminUser.createdAt
    };

    res.status(201).json({
      message: 'System initialized successfully',
      user: userResponse,
      username: username,
      loginInfo: {
        email: adminUser.email,
        username: username,
        role: 'superadmin'
      }
    });

  } catch (error) {
    console.error('System initialization error:', error);
    res.status(500).json({
      error: 'System initialization failed',
      message: 'An error occurred while initializing the system'
    });
  }
});

// Get system information
router.get('/info', async (req, res) => {
  try {
    const userCount = await User.count();
    const adminCount = await User.count({
      where: {
        role: ['admin', 'superadmin']
      }
    });

    res.json({
      version: '1.0.0',
      name: 'Quantix Platform',
      description: 'Protein Complex Analysis Platform',
      userCount,
      adminCount,
      isInitialized: adminCount > 0,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get system info error:', error);
    res.status(500).json({
      error: 'Failed to get system information',
      message: 'An error occurred while retrieving system information'
    });
  }
});

module.exports = router;

# 🐳 Docker 目录

这个目录包含了 Quantix 项目的 Docker 配置文件，用于容器化部署。

## 📋 文件列表

### 🏗️ 核心配置

#### `docker-compose.yml`

**用途**: Docker Compose 编排配置  
**服务**:

- **frontend**: Vue.js 前端应用
- **backend**: Node.js 后端 API
- **nginx**: 反向代理和静态文件服务
- **database**: MySQL 数据库 (可选)

```yaml
services:
  frontend:
    build: .
    ports:
      - '5173:5173'

  backend:
    build: ./server
    ports:
      - '3001:3001'
    environment:
      - NODE_ENV=production
```

#### `Dockerfile.frontend`

**用途**: 前端应用 Docker 镜像构建  
**功能**:

- 多阶段构建优化
- 生产环境优化
- 静态文件服务

```dockerfile
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
```

#### `nginx.conf`

**用途**: Nginx 配置文件  
**功能**:

- 反向代理配置
- 静态文件服务
- 路由重写规则
- 性能优化

```nginx
server {
    listen 80;
    server_name localhost;

    location / {
        root /usr/share/nginx/html;
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://backend:3001;
    }
}
```

## 🚀 使用指南

### 快速启动

```bash
# 使用脚本启动 (推荐)
./scripts/start-docker.sh    # Linux/macOS
./scripts/start-docker.bat   # Windows

# 手动启动
cd docker
docker-compose up -d
```

### 开发环境

```bash
# 启动开发环境（从项目根目录运行）
docker-compose -f docker/docker-compose.yml -f docker/docker-compose.dev.yml up

# 查看日志
docker-compose -f docker/docker-compose.yml logs -f

# 停止服务
docker-compose -f docker/docker-compose.yml down
```

### 生产环境

```bash
# 构建生产镜像（从项目根目录运行）
docker-compose -f docker/docker-compose.yml build --no-cache

# 启动生产环境
docker-compose -f docker/docker-compose.yml up -d

# 健康检查
docker-compose -f docker/docker-compose.yml ps
```

## 🔧 配置说明

### 环境变量

```bash
# .env 文件示例
NODE_ENV=production
DATABASE_URL=mysql://user:pass@db:3306/quantix
JWT_SECRET=your-secret-key
FRONTEND_URL=http://localhost:5173
BACKEND_URL=http://localhost:3001
```

### 端口映射

- **Frontend**: 5173 → 5173
- **Backend**: 3001 → 3001
- **Nginx**: 80 → 80
- **Database**: 3306 → 3306

### 数据卷

```yaml
volumes:
  - ./server/database.sqlite:/app/database.sqlite
  - ./uploads:/app/uploads
  - mysql_data:/var/lib/mysql
```

## 🏗️ 构建过程

### 前端构建

1. **依赖安装**: `npm ci --only=production`
2. **代码构建**: `npm run build`
3. **静态文件**: 复制到 Nginx 目录
4. **镜像优化**: 多阶段构建减小体积

### 后端构建

1. **基础镜像**: Node.js 18 Alpine
2. **依赖安装**: 生产环境依赖
3. **代码复制**: 应用代码和配置
4. **健康检查**: 内置健康检查端点

### 数据库配置

```yaml
database:
  image: mysql:8.0
  environment:
    MYSQL_ROOT_PASSWORD: rootpassword
    MYSQL_DATABASE: quantix
    MYSQL_USER: quantix
    MYSQL_PASSWORD: password
  volumes:
    - mysql_data:/var/lib/mysql
  ports:
    - '3306:3306'
```

## 🔍 监控和调试

### 查看日志

```bash
# 所有服务日志
docker-compose logs

# 特定服务日志
docker-compose logs frontend
docker-compose logs backend

# 实时日志
docker-compose logs -f
```

### 健康检查

```bash
# 检查容器状态
docker-compose ps

# 检查服务健康
curl http://localhost:3001/api/health
curl http://localhost:5173

# 进入容器调试
docker-compose exec backend sh
docker-compose exec frontend sh
```

### 性能监控

```bash
# 资源使用情况
docker stats

# 容器详细信息
docker-compose exec backend cat /proc/meminfo
docker-compose exec backend cat /proc/cpuinfo
```

## 🔧 自定义配置

### 开发环境覆盖

创建 `docker-compose.dev.yml`:

```yaml
version: '3.8'
services:
  frontend:
    command: npm run dev
    volumes:
      - ../src:/app/src
    environment:
      - NODE_ENV=development

  backend:
    command: npm run dev
    volumes:
      - ../server:/app
    environment:
      - NODE_ENV=development
```

### 生产环境优化

```yaml
services:
  frontend:
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  backend:
    restart: unless-stopped
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
```

## 🛡️ 安全配置

### 网络隔离

```yaml
networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
    internal: true

services:
  frontend:
    networks:
      - frontend

  backend:
    networks:
      - frontend
      - backend

  database:
    networks:
      - backend
```

### 密钥管理

```yaml
secrets:
  jwt_secret:
    file: ./secrets/jwt_secret.txt
  db_password:
    file: ./secrets/db_password.txt

services:
  backend:
    secrets:
      - jwt_secret
      - db_password
```

## 🚀 部署策略

### 滚动更新

```bash
# 更新单个服务
docker-compose up -d --no-deps backend

# 零停机更新
docker-compose up -d --scale backend=2
docker-compose stop backend_1
docker-compose up -d --scale backend=1
```

### 备份和恢复

```bash
# 数据库备份
docker-compose exec database mysqldump -u root -p quantix > backup.sql

# 数据卷备份
docker run --rm -v mysql_data:/data -v $(pwd):/backup alpine tar czf /backup/data.tar.gz /data

# 恢复数据
docker-compose exec database mysql -u root -p quantix < backup.sql
```

## 🐛 故障排除

### 常见问题

1. **端口冲突**: 检查端口是否被占用
2. **权限错误**: 确保 Docker 有足够权限
3. **内存不足**: 增加 Docker 内存限制
4. **网络问题**: 检查防火墙和网络配置

### 调试命令

```bash
# 检查 Docker 状态
docker version
docker-compose version

# 清理资源
docker system prune -a
docker volume prune

# 重建镜像
docker-compose build --no-cache
docker-compose up --force-recreate
```

## 📊 性能优化

### 镜像优化

- 使用 Alpine Linux 基础镜像
- 多阶段构建减小体积
- 合并 RUN 指令减少层数
- 使用 .dockerignore 排除不必要文件

### 运行时优化

- 设置合适的资源限制
- 使用健康检查确保服务可用
- 配置重启策略
- 启用日志轮转

## 📞 支持

如果遇到 Docker 相关问题，请：

1. 检查 Docker 和 Docker Compose 版本
2. 查看容器日志输出
3. 确认端口和网络配置
4. 联系开发团队获取支持

---

**维护**: Quantix 开发团队  
**更新**: 2025-06-19

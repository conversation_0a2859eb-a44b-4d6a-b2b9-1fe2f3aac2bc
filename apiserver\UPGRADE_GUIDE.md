# Quantix 支付系统升级指南

本指南帮助您将老版本的 Quantix 系统升级到新的支付架构（申请与支付订单合并）。

## 🔍 升级概述

### 架构变更
- **老版本**: 独立的 `payments` 表 + `applications` 表
- **新版本**: 合并到 `applications` 表，移除 `payments` 表

### 主要改进
- 简化数据模型，减少表关联
- 提高数据一致性
- 支持申请重复支付
- 更好的支付状态管理

## 🚨 升级前准备

### 1. 备份数据库
```bash
# SQLite 备份
cp database.sqlite database-backup-$(date +%Y%m%d-%H%M%S).sqlite

# MySQL 备份
mysqldump -u username -p quantix > quantix-backup-$(date +%Y%m%d-%H%M%S).sql
```

### 2. 检查系统兼容性
```bash
# 检查当前系统状态
node scripts/check-compatibility.js --verbose

# 预览升级操作
node scripts/upgrade-payment-system.js --dry-run
```

## 🔄 升级步骤

### 自动升级（推荐）

#### 1. 使用升级脚本
```bash
# 预览升级操作（安全，不会修改数据）
node scripts/upgrade-payment-system.js --dry-run

# 执行升级（有支付数据时需要 --force）
node scripts/upgrade-payment-system.js --force
```

#### 2. 启用自动兼容性修复
```bash
# 设置环境变量启用自动修复
export AUTO_FIX_COMPATIBILITY=true

# 或在 .env 文件中添加
echo "AUTO_FIX_COMPATIBILITY=true" >> .env

# 启动系统时会自动检查和修复
npm start
```

### 手动升级

#### 1. 全新安装
如果是全新安装，系统会自动添加支付字段：
```bash
# 启动系统，会自动初始化
npm start
```

#### 2. 老版本升级
对于有 `payments` 表的老版本：

```sql
-- 1. 添加支付字段到 applications 表
ALTER TABLE applications ADD COLUMN paymentOrderId VARCHAR(50);
ALTER TABLE applications ADD COLUMN paymentMethod TEXT;
ALTER TABLE applications ADD COLUMN paymentStatus TEXT;
-- ... 其他字段

-- 2. 迁移数据
UPDATE applications 
SET paymentOrderId = (SELECT paymentId FROM payments WHERE payments.applicationId = applications.id),
    paymentMethod = (SELECT paymentMethod FROM payments WHERE payments.applicationId = applications.id),
    paymentStatus = (SELECT status FROM payments WHERE payments.applicationId = applications.id)
WHERE id IN (SELECT applicationId FROM payments);

-- 3. 删除 payments 表
DROP TABLE payments;
```

## 🔧 故障排除

### 常见问题

#### 1. "no such column: paymentOrderId" 错误
**原因**: 数据库结构未更新
**解决**: 
```bash
node scripts/upgrade-payment-system.js --force
```

#### 2. "payments table still exists" 警告
**原因**: 升级未完成
**解决**: 
```bash
# 检查数据是否已迁移
node scripts/check-compatibility.js --verbose

# 如果数据已迁移，可以手动删除 payments 表
# 注意：请确认数据已正确迁移后再执行
```

#### 3. 支付功能异常
**检查步骤**:
1. 验证数据库结构：`node scripts/check-compatibility.js`
2. 检查支付字段：确认 `applications` 表包含所有支付字段
3. 验证数据迁移：检查支付记录是否正确迁移

### 回滚操作

如果升级后出现问题，可以回滚到备份：

```bash
# SQLite 回滚
cp database-backup-YYYYMMDD-HHMMSS.sqlite database.sqlite

# MySQL 回滚
mysql -u username -p quantix < quantix-backup-YYYYMMDD-HHMMSS.sql
```

## 📊 验证升级

### 1. 系统兼容性检查
```bash
node scripts/check-compatibility.js
```

### 2. 功能测试
```bash
# 运行支付集成测试
npm test -- tests/payment-integration.test.js
```

### 3. 手动验证
1. 创建测试申请
2. 发起支付订单
3. 模拟支付成功
4. 验证报告下载权限

## 🔒 安全注意事项

### 数据安全
- 升级前必须备份数据库
- 在测试环境先验证升级过程
- 生产环境升级时建议停机维护

### 兼容性
- 新版本向后兼容老版本的 API
- 前端无需修改
- 第三方集成无需更新

## 📞 技术支持

如果在升级过程中遇到问题：

1. 查看系统日志：`tail -f logs/app.log`
2. 运行诊断工具：`node scripts/check-compatibility.js --verbose`
3. 检查数据库状态：确认表结构和数据完整性

## 🎯 升级后的新功能

### 支付重试
- 用户可以对同一申请多次发起支付
- 支持支付失败后重新支付

### 简化的数据模型
- 减少了表关联查询
- 提高了数据一致性
- 简化了业务逻辑

### 更好的审计跟踪
- 完整的支付历史记录
- 详细的支付状态变更日志
- 支持退款管理

---

**重要提醒**: 升级是不可逆的操作，请务必在升级前备份数据库！

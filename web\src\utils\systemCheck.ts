// 系统初始化检查工具
import { systemAPI } from '@/services/api'
import type { SystemInitStatus } from '@/types/api'

export type SystemStatus = SystemInitStatus

// 检查系统是否已初始化
export async function checkSystemInitialization(): Promise<SystemStatus> {
  try {
    const response = await systemAPI.checkInitStatus()
    return response.data
  } catch (error) {
    console.error('System check error:', error)
    // 如果检查失败，假设系统未初始化
    return {
      isInitialized: false,
      adminCount: 0,
      message: 'Unable to check system status',
    }
  }
}

// 检查是否需要重定向到初始化页面
export async function checkAndRedirectToInit(): Promise<boolean> {
  const status = await checkSystemInitialization()

  if (!status.isInitialized) {
    // 系统未初始化，需要重定向
    return true
  }

  return false
}

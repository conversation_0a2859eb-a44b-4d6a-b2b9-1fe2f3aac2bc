const express = require('express');
const { body, validationResult } = require('express-validator');
const { authenticate } = require('../middleware/auth');
const { Application } = require('../models');
const WeChatPayService = require('../services/wechatPayService');
const AlipayService = require('../services/alipayService');
const router = express.Router();

// 初始化支付服务
const wechatPayService = new WeChatPayService();
const alipayService = new AlipayService();

// 检查支付服务是否正确配置
function isPaymentConfigured() {
  // 检查微信支付配置
  const wechatConfigured = !!(
    process.env.WECHAT_APPID &&
    process.env.WECHAT_MCHID &&
    process.env.WECHAT_PRIVATE_KEY &&
    process.env.WECHAT_APPID !== 'your_wechat_appid' &&
    process.env.WECHAT_MCHID !== 'your_merchant_id'
  );

  // 检查支付宝配置
  const alipayConfigured = !!(
    process.env.ALIPAY_APPID &&
    process.env.ALIPAY_PRIVATE_KEY &&
    process.env.ALIPAY_PUBLIC_KEY &&
    process.env.ALIPAY_APPID !== 'your_alipay_appid'
  );

  return wechatConfigured || alipayConfigured;
}

// 验证支付请求的中间件
const validatePaymentRequest = [
  body('applicationId')
    .isInt({ min: 1 })
    .withMessage('Application ID must be a positive integer'),
  body('paymentMethod')
    .isIn(['wechat', 'alipay'])
    .withMessage('Payment method must be either wechat or alipay'),
  body('amount')
    .isFloat({ min: 0.01 })
    .withMessage('Amount must be greater than 0.01')
];

// 创建支付订单
router.post(
  '/create',
  authenticate,
  validatePaymentRequest,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { applicationId, paymentMethod, amount } = req.body;
      const userId = req.user.id;

      // 验证申请是否存在且属于当前用户
      const application = await Application.findOne({
        where: {
          id: applicationId,
          userId: userId
        }
      });

      if (!application) {
        return res.status(404).json({
          error: 'Application not found',
          message:
            'The specified application does not exist or does not belong to you'
        });
      }

      // 检查申请状态是否为已完成
      if (application.status !== 'completed') {
        return res.status(400).json({
          error: 'Invalid application status',
          message: 'Payment can only be made for completed applications'
        });
      }

      // 检查是否已经支付
      if (
        application.reportPaymentStatus === 'paid' ||
        application.reportPaymentStatus === 'free'
      ) {
        return res.status(400).json({
          error: 'Already paid',
          message: 'This report has already been paid for or is free'
        });
      }

      // 验证金额是否正确
      if (parseFloat(amount) !== parseFloat(application.reportPrice)) {
        return res.status(400).json({
          error: 'Invalid amount',
          message: 'Payment amount does not match the report price'
        });
      }

      // 检查是否有未完成的支付订单
      if (application.paymentStatus === 'pending' && !application.isPaymentExpired()) {
        return res.status(400).json({
          error: 'Payment already exists',
          message: 'There is already a pending payment for this application',
          paymentId: application.paymentOrderId
        });
      }

      // 获取客户端信息
      const clientIp = req.ip || req.connection.remoteAddress;
      const userAgent = req.get('User-Agent');

      // 初始化支付信息
      application.initializePayment(paymentMethod, clientIp, userAgent);
      await application.save();

      // 根据支付方式生成支付二维码
      let qrCodeContent = '';
      let qrCodeUrl = '';
      let thirdPartyOrderId = '';
      let paymentResult = null;
      const description = `蛋白质分析报告 - ${application.applicationId}`;

      if (paymentMethod === 'wechat') {
        // 使用微信支付服务创建订单
        paymentResult = await wechatPayService.createNativeOrder({
          paymentId: application.paymentOrderId,
          amount: amount,
          description: description,
          userId: userId
        });

        if (paymentResult.success) {
          qrCodeContent = paymentResult.qrCodeContent;
          qrCodeUrl = `${req.protocol}://${req.get('host')}/api/payments/${application.paymentOrderId}/qrcode`;
          thirdPartyOrderId = application.paymentOrderId;
        } else {
          throw new Error(`WeChat Pay error: ${paymentResult.error}`);
        }
      } else if (paymentMethod === 'alipay') {
        // 使用支付宝服务创建订单
        paymentResult = await alipayService.createQRPayOrder({
          paymentId: application.paymentOrderId,
          amount: amount,
          description: description,
          userId: userId
        });

        if (paymentResult.success) {
          qrCodeContent = paymentResult.qrCodeContent;
          qrCodeUrl = `${req.protocol}://${req.get('host')}/api/payments/${application.paymentOrderId}/qrcode`;
          thirdPartyOrderId = application.paymentOrderId;
        } else {
          throw new Error(`Alipay error: ${paymentResult.error}`);
        }
      }

      // 更新申请的二维码信息
      await application.update({
        qrCodeContent: qrCodeContent,
        qrCodeUrl: qrCodeUrl,
        thirdPartyOrderId: thirdPartyOrderId
      });

      res.status(201).json({
        success: true,
        message: 'Payment order created successfully',
        payment: {
          paymentId: application.paymentOrderId,
          amount: amount,
          currency: 'CNY',
          paymentMethod: application.paymentMethod,
          status: application.paymentStatus,
          qrCodeUrl: application.qrCodeUrl,
          qrCodeContent: application.qrCodeContent,
          expiresAt: application.paymentExpiresAt,
          description: description
        }
      });
    } catch (error) {
      console.error('Create payment error:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to create payment order'
      });
    }
  }
);

// 查询支付状态
router.get('/:paymentId/status', authenticate, async (req, res) => {
  try {
    const { paymentId } = req.params;
    const userId = req.user.id;

    const application = await Application.findOne({
      where: {
        paymentOrderId: paymentId,
        userId: userId
      }
    });

    if (!application) {
      return res.status(404).json({
        error: 'Payment not found',
        message: 'The specified payment does not exist'
      });
    }

    // 检查支付是否过期
    if (application.isPaymentExpired() && application.paymentStatus === 'pending') {
      application.markPaymentExpired();
      await application.save();
    }

    res.json({
      success: true,
      payment: {
        paymentId: application.paymentOrderId,
        amount: application.reportPrice,
        currency: 'CNY',
        paymentMethod: application.paymentMethod,
        status: application.paymentStatus,
        qrCodeUrl: application.qrCodeUrl,
        expiresAt: application.paymentExpiresAt,
        paidAt: application.paidAt,
        description: `蛋白质分析报告 - ${application.applicationId}`,
        application: {
          id: application.id,
          applicationId: application.applicationId,
          title: application.applicationId
        }
      }
    });
  } catch (error) {
    console.error('Get payment status error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to get payment status'
    });
  }
});

// 生成支付二维码图片
router.get('/:paymentId/qrcode', async (req, res) => {
  try {
    const { paymentId } = req.params;

    const application = await Application.findOne({
      where: { paymentOrderId: paymentId }
    });

    if (!application) {
      return res.status(404).json({
        error: 'Payment not found',
        message: 'The specified payment does not exist'
      });
    }

    if (!application.qrCodeContent) {
      return res.status(400).json({
        error: 'QR code not available',
        message: 'QR code has not been generated for this payment'
      });
    }

    // 使用qrcode库生成实际的二维码图片
    const QRCode = require('qrcode');

    try {
      // 生成PNG格式的二维码图片
      const qrCodeBuffer = await QRCode.toBuffer(application.qrCodeContent, {
        type: 'png',
        width: 256,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });

      // 设置响应头为图片类型和CORS头
      res.setHeader('Content-Type', 'image/png');
      res.setHeader('Content-Length', qrCodeBuffer.length);
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Methods', 'GET');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

      // 返回二维码图片
      res.send(qrCodeBuffer);
    } catch (qrError) {
      console.error('QR code generation error:', qrError);
      res.status(500).json({
        error: 'QR code generation failed',
        message: 'Failed to generate QR code image'
      });
    }
  } catch (error) {
    console.error('Get QR code error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to get QR code'
    });
  }
});

// 支付回调接口（微信支付）
router.post('/callback/wechat', async (req, res) => {
  try {
    // 这里应该验证微信支付的签名
    const { out_trade_no, transaction_id, result_code, return_code } = req.body;

    if (return_code === 'SUCCESS' && result_code === 'SUCCESS') {
      const application = await Application.findByThirdPartyOrderId(out_trade_no);

      if (application && application.paymentStatus === 'pending') {
        application.markPaymentPaid(transaction_id, req.body);
        await application.save();
      }
    }

    // 返回微信要求的格式
    res.set('Content-Type', 'text/xml');
    res.send(
      '<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>'
    );
  } catch (error) {
    console.error('WeChat payment callback error:', error);
    res.set('Content-Type', 'text/xml');
    res.send(
      '<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[ERROR]]></return_msg></xml>'
    );
  }
});

// 支付回调接口（支付宝）
router.post('/callback/alipay', async (req, res) => {
  try {
    // 这里应该验证支付宝的签名
    const { out_trade_no, trade_no, trade_status } = req.body;

    if (trade_status === 'TRADE_SUCCESS') {
      const application = await Application.findByThirdPartyOrderId(out_trade_no);

      if (application && application.paymentStatus === 'pending') {
        application.markPaymentPaid(trade_no, req.body);
        await application.save();
      }
    }

    res.send('success');
  } catch (error) {
    console.error('Alipay payment callback error:', error);
    res.send('fail');
  }
});

// 取消支付订单
router.post('/:paymentId/cancel', authenticate, async (req, res) => {
  try {
    const { paymentId } = req.params;
    const userId = req.user.id;

    const application = await Application.findOne({
      where: {
        paymentOrderId: paymentId,
        userId: userId
      }
    });

    if (!application) {
      return res.status(404).json({
        error: 'Payment not found',
        message: 'The specified payment does not exist'
      });
    }

    if (application.paymentStatus !== 'pending') {
      return res.status(400).json({
        error: 'Cannot cancel payment',
        message: 'Only pending payments can be cancelled'
      });
    }

    await application.update({ paymentStatus: 'cancelled' });

    res.json({
      success: true,
      message: 'Payment cancelled successfully'
    });
  } catch (error) {
    console.error('Cancel payment error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to cancel payment'
    });
  }
});

// 获取用户的支付历史
router.get('/history', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 10, status } = req.query;

    const whereClause = {
      userId: userId,
      paymentOrderId: { [require('sequelize').Op.ne]: null }
    };
    if (status) {
      whereClause.paymentStatus = status;
    }

    const applications = await Application.findAndCountAll({
      where: whereClause,
      attributes: [
        'id', 'applicationId', 'paymentOrderId', 'paymentMethod',
        'paymentStatus', 'reportPrice', 'paidAt', 'paymentExpiresAt',
        'createdAt', 'updatedAt'
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit)
    });

    // 转换为支付历史格式
    const payments = applications.rows.map(app => ({
      paymentId: app.paymentOrderId,
      amount: app.reportPrice,
      currency: 'CNY',
      paymentMethod: app.paymentMethod,
      status: app.paymentStatus,
      paidAt: app.paidAt,
      expiresAt: app.paymentExpiresAt,
      createdAt: app.createdAt,
      updatedAt: app.updatedAt,
      application: {
        id: app.id,
        applicationId: app.applicationId,
        title: app.applicationId
      }
    }));

    res.json({
      success: true,
      payments: payments,
      pagination: {
        total: applications.count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(applications.count / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Get payment history error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to get payment history'
    });
  }
});

// 模拟支付成功（仅用于测试）
router.post('/:paymentId/simulate-success', authenticate, async (req, res) => {
  try {
    // 只有在没有配置真实支付时才允许模拟支付
    if (isPaymentConfigured()) {
      return res.status(403).json({
        error: 'Not allowed',
        message: 'Simulate payment is not available when real payment services are configured'
      });
    }

    const { paymentId } = req.params;
    const userId = req.user.id;

    const application = await Application.findOne({
      where: {
        paymentOrderId: paymentId,
        userId: userId
      }
    });

    if (!application) {
      return res.status(404).json({
        error: 'Payment not found',
        message: 'The specified payment does not exist'
      });
    }

    if (application.paymentStatus !== 'pending') {
      return res.status(400).json({
        error: 'Cannot simulate payment',
        message: 'Only pending payments can be simulated'
      });
    }

    application.markPaymentPaid(`SIMULATE_${Date.now()}`, { simulated: true });
    await application.save();

    res.json({
      success: true,
      message: 'Payment simulated successfully',
      payment: {
        paymentId: application.paymentOrderId,
        status: application.paymentStatus,
        paidAt: application.paidAt
      }
    });
  } catch (error) {
    console.error('Simulate payment error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to simulate payment'
    });
  }
});

// 检查是否启用模拟支付
router.get('/simulate-enabled', (req, res) => {
  res.json({
    enabled: !isPaymentConfigured(),
    message: isPaymentConfigured()
      ? 'Real payment services are configured'
      : 'Simulate payment is available'
  });
});

module.exports = router;

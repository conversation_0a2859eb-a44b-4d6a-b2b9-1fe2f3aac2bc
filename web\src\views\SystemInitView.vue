<template>
  <div class="system-init-container">
    <div class="init-card">
      <div class="init-header">
        <h1>🚀 系统初始化</h1>
        <p>欢迎使用 Quantix 蛋白质分析平台</p>
        <p class="subtitle">请设置您的超级管理员账户</p>
      </div>

      <div v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>

      <div v-if="successMessage" class="success-message">
        {{ successMessage }}
      </div>

      <form @submit.prevent="handleInitialize" class="init-form">
        <div class="form-group">
          <label for="username">管理员用户名 *</label>
          <input
            type="text"
            id="username"
            v-model="initForm.username"
            :disabled="isLoading"
            placeholder="请输入管理员用户名"
            required
          />
          <small class="form-help">用户名只能包含字母、数字、下划线和连字符</small>
        </div>

        <div class="form-group">
          <label for="email">邮箱地址 *</label>
          <input
            type="email"
            id="email"
            v-model="initForm.email"
            :disabled="isLoading"
            placeholder="请输入管理员邮箱"
            required
          />
        </div>

        <div class="form-group">
          <label for="password">密码 *</label>
          <input
            type="password"
            id="password"
            v-model="initForm.password"
            :disabled="isLoading"
            placeholder="请输入密码（至少6位）"
            required
          />
        </div>

        <div class="form-group">
          <label for="confirmPassword">确认密码 *</label>
          <input
            type="password"
            id="confirmPassword"
            v-model="initForm.confirmPassword"
            :disabled="isLoading"
            placeholder="请再次输入密码"
            required
          />
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="firstName">姓名</label>
            <input
              type="text"
              id="firstName"
              v-model="initForm.firstName"
              :disabled="isLoading"
              placeholder="姓"
            />
          </div>
          <div class="form-group">
            <label for="lastName">名字</label>
            <input
              type="text"
              id="lastName"
              v-model="initForm.lastName"
              :disabled="isLoading"
              placeholder="名"
            />
          </div>
        </div>

        <div class="form-info">
          <h4>🔐 权限说明</h4>
          <ul>
            <li>超级管理员拥有系统的最高权限</li>
            <li>可以管理所有用户和系统设置</li>
            <li>可以查看所有分析申请和报告</li>
            <li>请妥善保管您的登录信息</li>
          </ul>
        </div>

        <button type="submit" class="init-btn" :disabled="isLoading || !isFormValid">
          <span v-if="isLoading">初始化中...</span>
          <span v-else>🚀 初始化系统</span>
        </button>
      </form>

      <div class="init-footer">
        <p>初始化完成后，您将自动跳转到管理员登录页面</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { systemAPI } from '@/services/api'

export default defineComponent({
  name: 'SystemInitView',
  setup() {
    const router = useRouter()
    const isLoading = ref(false)
    const errorMessage = ref('')
    const successMessage = ref('')

    const initForm = reactive({
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      firstName: '',
      lastName: '',
    })

    const isFormValid = computed(() => {
      return (
        initForm.username.length >= 3 &&
        initForm.email.includes('@') &&
        initForm.password.length >= 6 &&
        initForm.password === initForm.confirmPassword
      )
    })

    const handleInitialize = async () => {
      if (!isFormValid.value) {
        errorMessage.value = '请填写所有必填字段并确保密码一致'
        return
      }

      isLoading.value = true
      errorMessage.value = ''
      successMessage.value = ''

      try {
        const response = await systemAPI.initialize({
          email: initForm.email,
          password: initForm.password,
          firstName: initForm.firstName,
          lastName: initForm.lastName,
        })

        const data = response.data

        successMessage.value = '系统初始化成功！正在跳转到登录页面...'

        // 清空表单
        Object.keys(initForm).forEach((key) => {
          initForm[key as keyof typeof initForm] = ''
        })

        // 延迟跳转到统一登录页面
        setTimeout(() => {
          router.push({
            path: '/login',
            query: {
              email: data.user.email,
              initialized: 'true',
            },
          })
        }, 2000)
      } catch (error: any) {
        errorMessage.value = error.message || '初始化失败，请重试'
      } finally {
        isLoading.value = false
      }
    }

    return {
      initForm,
      isLoading,
      errorMessage,
      successMessage,
      isFormValid,
      handleInitialize,
    }
  },
})
</script>

<style scoped>
.system-init-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.init-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.init-header {
  text-align: center;
  margin-bottom: 30px;
}

.init-header h1 {
  color: #333;
  margin: 0 0 10px 0;
  font-size: 32px;
}

.init-header p {
  color: #666;
  margin: 5px 0;
  font-size: 16px;
}

.subtitle {
  font-weight: 600;
  color: #444 !important;
}

.error-message {
  background-color: #fee;
  color: #c33;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 20px;
  border: 1px solid #fcc;
  text-align: center;
}

.success-message {
  background-color: #efe;
  color: #3c3;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 20px;
  border: 1px solid #cfc;
  text-align: center;
}

.init-form {
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-weight: 600;
}

.form-group input {
  width: 100%;
  padding: 12px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
}

.form-group input:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.form-help {
  display: block;
  margin-top: 5px;
  color: #666;
  font-size: 12px;
}

.form-info {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 25px;
}

.form-info h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 16px;
}

.form-info ul {
  margin: 0;
  padding-left: 20px;
}

.form-info li {
  color: #666;
  font-size: 14px;
  margin-bottom: 5px;
}

.init-btn {
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.init-btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

.init-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.init-footer {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.init-footer p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

@media (max-width: 768px) {
  .init-card {
    padding: 30px 20px;
    margin: 10px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .init-header h1 {
    font-size: 28px;
  }
}
</style>

<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="1920" height="1080" fill="#E0F2F7"/>
<g opacity="0.8" filter="url(#filter0_f_1_2)">
<ellipse cx="960" cy="540" rx="700" ry="400" fill="#81D4FA"/>
</g>
<g opacity="0.6" filter="url(#filter1_f_1_2)">
<ellipse cx="400" cy="800" rx="300" ry="200" fill="#4FC3F7"/>
</g>
<g opacity="0.7" filter="url(#filter2_f_1_2)">
<ellipse cx="1500" cy="300" rx="400" ry="250" fill="#29B6F6"/>
</g>
<g opacity="0.5" filter="url(#filter3_f_1_2)">
<ellipse cx="100" cy="100" rx="150" ry="100" fill="#03A9F4"/>
</g>
<g opacity="0.4" filter="url(#filter4_f_1_2)">
<ellipse cx="1800" cy="900" rx="200" ry="150" fill="#0288D1"/>
</g>
<defs>
<filter id="filter0_f_1_2" x="260" y="140" width="1400" height="800" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_1_2"/>
</filter>
<filter id="filter1_f_1_2" x="0" y="500" width="800" height="600" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_1_2"/>
</filter>
<filter id="filter2_f_1_2" x="900" y="0" width="1200" height="700" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_1_2"/>
</filter>
<filter id="filter3_f_1_2" x="-50" y="-50" width="400" height="300" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_1_2"/>
</filter>
<filter id="filter4_f_1_2" x="1500" y="650" width="600" height="500" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_1_2"/>
</filter>
</defs>
</svg>
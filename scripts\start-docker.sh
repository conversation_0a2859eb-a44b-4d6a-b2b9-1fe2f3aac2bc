#!/bin/bash

echo "🐳 启动 Quantix 平台 - Docker 模式"
echo

# 检查Docker是否运行
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装"
    echo "   请先安装 Docker"
    echo "   macOS: brew install docker"
    echo "   Ubuntu: sudo apt-get install docker.io"
    echo "   或访问: https://www.docker.com/products/docker-desktop"
    exit 1
fi

if ! docker info &> /dev/null; then
    echo "❌ Docker 未运行"
    echo "   请启动 Docker 服务"
    exit 1
fi

echo "✅ Docker 已就绪"

# 检查环境变量文件
if [ ! -f ".env" ]; then
    echo "📝 创建环境变量文件..."
    cp ".env.docker" ".env"
    echo
    echo "⚠️  请编辑 .env 文件并设置以下必需的环境变量:"
    echo "   - EMAIL_USER: 您的邮箱地址"
    echo "   - EMAIL_PASS: 您的邮箱应用密码"
    echo "   - OPENAI_API_KEY: 您的 OpenAI API 密钥"
    echo
    echo "按 Enter 继续使用默认配置，或按 Ctrl+C 退出并编辑 .env 文件..."
    read
fi

echo "🏗️ 构建和启动容器..."
docker-compose -f docker/docker-compose.yml down
docker-compose -f docker/docker-compose.yml build --no-cache
docker-compose -f docker/docker-compose.yml up -d

if [ $? -ne 0 ]; then
    echo "❌ Docker 容器启动失败"
    echo "查看错误日志: docker-compose -f docker/docker-compose.yml logs"
    exit 1
fi

echo "⏳ 等待服务启动..."
sleep 30

echo "🔍 检查服务状态..."
docker-compose -f docker/docker-compose.yml ps

echo
echo "🎉 Quantix 平台已启动！"
echo
echo "📊 前端访问地址: http://localhost"
echo "🔧 后端API地址: http://localhost/api"
echo "🔐 管理员登录: http://localhost/admin/login"
echo "📚 API健康检查: http://localhost/api/health"
echo
echo "🔑 默认管理员账户:"
echo "   用户名: admin"
echo "   密码: fHadmin"
echo "   邮箱: <EMAIL>"
echo
echo "📋 Docker 管理命令:"
echo "   查看日志: docker-compose logs"
echo "   停止服务: docker-compose down"
echo "   重启服务: docker-compose restart"
echo "   查看状态: docker-compose ps"
echo

# 等待MySQL完全启动
echo "⏳ 等待数据库初始化..."
sleep 10

echo "🗄️ 初始化数据库和管理员账户..."
docker-compose exec backend node scripts/init-database.js

echo
echo "✅ 平台启动完成！"
echo
echo "要停止所有服务，请运行: docker-compose down"

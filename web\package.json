{"name": "quantix", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "playwright test", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/", "server:dev": "nodemon server/index.js", "server:start": "node server/index.js", "server:test": "jest server/tests/", "server:install": "cd server && npm install", "install:all": "npm install && npm run server:install", "start:dev": "concurrently \"npm run server:dev\" \"npm run dev\"", "setup:db": "node server/scripts/setup-database.js"}, "dependencies": {"axios": "^1.10.0", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-i18n": "^11.1.5", "vue-router": "^4.5.0"}, "devDependencies": {"@playwright/test": "^1.51.1", "@tsconfig/node22": "^22.0.1", "@types/jsdom": "^21.1.7", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vitest/eslint-plugin": "^1.1.39", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "concurrently": "^9.1.2", "eslint": "^9.22.0", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-vue": "^10.2.0", "jiti": "^2.4.2", "jsdom": "^26.0.0", "npm-run-all2": "^8.0.4", "prettier": "^3.6.0", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.1.1", "vue-tsc": "^2.2.8"}, "overrides": {"glob": "^10.4.5", "inflight": "npm:@isaacs/inflight@^1.0.6"}}
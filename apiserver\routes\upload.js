const express = require('express');
const path = require('path');
const fs = require('fs');
const { authenticate, requireEmailVerification } = require('../middleware/auth');
const {
  upload,
  handleUploadError,
  determineFileType,
  getFileStats
} = require('../middleware/upload');

const router = express.Router();

// Upload files endpoint
router.post(
  '/files',
  authenticate,
  requireEmailVerification,
  upload.array('files', 5), // Allow up to 5 files
  handleUploadError,
  async (req, res) => {
    try {
      if (!req.files || req.files.length === 0) {
        return res.status(400).json({
          error: 'No files uploaded',
          message: 'Please select at least one file to upload'
        });
      }

      const uploadedFiles = [];

      for (const file of req.files) {
        // Determine file type
        const fileType = determineFileType(file.path, file.originalname);

        // Get file statistics
        const stats = getFileStats(file.path);

        if (!stats) {
          // Clean up the file if we can't read it
          try {
            fs.unlinkSync(file.path);
          } catch (error) {
            console.error('Failed to cleanup invalid file:', error);
          }
          continue;
        }

        if (stats.isEmpty) {
          // Clean up empty files
          try {
            fs.unlinkSync(file.path);
          } catch (error) {
            console.error('Failed to cleanup empty file:', error);
          }
          continue;
        }

        const fileInfo = {
          originalName: file.originalname,
          filename: file.filename,
          path: file.path,
          size: file.size,
          mimetype: file.mimetype,
          fileType: fileType,
          stats: stats,
          uploadedAt: new Date(),
          isProcessed: false
        };

        uploadedFiles.push(fileInfo);
      }

      if (uploadedFiles.length === 0) {
        return res.status(400).json({
          error: 'No valid files uploaded',
          message: 'All uploaded files were invalid or empty'
        });
      }

      res.json({
        message: `Successfully uploaded ${uploadedFiles.length} file(s)`,
        files: uploadedFiles.map((file) => ({
          originalName: file.originalName,
          filename: file.filename,
          path: file.path,
          size: file.size,
          fileType: file.fileType,
          uploadedAt: file.uploadedAt,
          stats: {
            lineCount: file.stats.lineCount,
            characterCount: file.stats.characterCount
          }
        }))
      });
    } catch (error) {
      console.error('File upload error:', error);

      // Clean up any uploaded files on error
      if (req.files) {
        req.files.forEach((file) => {
          try {
            fs.unlinkSync(file.path);
          } catch (cleanupError) {
            console.error('Failed to cleanup file on error:', cleanupError);
          }
        });
      }

      res.status(500).json({
        error: 'Upload failed',
        message: 'An error occurred during file upload'
      });
    }
  }
);

// Get file information
router.get('/files/:filename', authenticate, async (req, res) => {
  try {
    const { filename } = req.params;
    const userDir = path.join(process.env.UPLOAD_PATH || './server/uploads', req.user.id.toString());
    const filePath = path.join(userDir, filename);

    // Check if file exists and belongs to the user
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        error: 'File not found',
        message: 'The requested file does not exist'
      });
    }

    const stats = fs.statSync(filePath);
    const fileType = determineFileType(filePath, filename);
    const fileStats = getFileStats(filePath);

    res.json({
      filename: filename,
      size: stats.size,
      fileType: fileType,
      createdAt: stats.birthtime,
      modifiedAt: stats.mtime,
      stats: fileStats
    });
  } catch (error) {
    console.error('Get file info error:', error);
    res.status(500).json({
      error: 'Failed to get file information',
      message: 'An error occurred while retrieving file information'
    });
  }
});

// Download file
router.get('/files/:filename/download', authenticate, async (req, res) => {
  try {
    const { filename } = req.params;
    const userDir = path.join(process.env.UPLOAD_PATH || './server/uploads', req.user.id.toString());
    const filePath = path.join(userDir, filename);

    // Check if file exists and belongs to the user
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        error: 'File not found',
        message: 'The requested file does not exist'
      });
    }

    // Get original filename from database or use current filename
    const originalName = filename; // In a real app, you'd get this from the database

    res.download(filePath, originalName, (error) => {
      if (error) {
        console.error('File download error:', error);
        if (!res.headersSent) {
          res.status(500).json({
            error: 'Download failed',
            message: 'An error occurred during file download'
          });
        }
      }
    });
  } catch (error) {
    console.error('Download file error:', error);
    res.status(500).json({
      error: 'Download failed',
      message: 'An error occurred while preparing file download'
    });
  }
});

// Delete file
router.delete('/files/:filename', authenticate, async (req, res) => {
  try {
    const { filename } = req.params;
    const userDir = path.join(process.env.UPLOAD_PATH || './server/uploads', req.user.id.toString());
    const filePath = path.join(userDir, filename);

    // Check if file exists and belongs to the user
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        error: 'File not found',
        message: 'The requested file does not exist'
      });
    }

    // Delete the file
    fs.unlinkSync(filePath);

    res.json({
      message: 'File deleted successfully',
      filename: filename
    });
  } catch (error) {
    console.error('Delete file error:', error);
    res.status(500).json({
      error: 'Delete failed',
      message: 'An error occurred while deleting the file'
    });
  }
});

// List user's files
router.get('/files', authenticate, async (req, res) => {
  try {
    const userDir = path.join(process.env.UPLOAD_PATH || './server/uploads', req.user.id.toString());

    if (!fs.existsSync(userDir)) {
      return res.json({
        files: [],
        totalCount: 0,
        totalSize: 0
      });
    }

    const files = fs.readdirSync(userDir);
    const fileInfos = [];
    let totalSize = 0;

    for (const filename of files) {
      const filePath = path.join(userDir, filename);
      const stats = fs.statSync(filePath);

      if (stats.isFile()) {
        const fileType = determineFileType(filePath, filename);
        const fileStats = getFileStats(filePath);

        fileInfos.push({
          filename: filename,
          size: stats.size,
          fileType: fileType,
          createdAt: stats.birthtime,
          modifiedAt: stats.mtime,
          stats: fileStats
            ? {
              lineCount: fileStats.lineCount,
              characterCount: fileStats.characterCount
            }
            : null
        });

        totalSize += stats.size;
      }
    }

    // Sort by creation date (newest first)
    fileInfos.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    res.json({
      files: fileInfos,
      totalCount: fileInfos.length,
      totalSize: totalSize
    });
  } catch (error) {
    console.error('List files error:', error);
    res.status(500).json({
      error: 'Failed to list files',
      message: 'An error occurred while retrieving file list'
    });
  }
});

// Validate file format
router.post('/files/:filename/validate', authenticate, async (req, res) => {
  try {
    const { filename } = req.params;
    const userDir = path.join(process.env.UPLOAD_PATH || './server/uploads', req.user.id.toString());
    const filePath = path.join(userDir, filename);

    // Check if file exists and belongs to the user
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        error: 'File not found',
        message: 'The requested file does not exist'
      });
    }

    const fileType = determineFileType(filePath, filename);
    const fileStats = getFileStats(filePath);

    let validationResult = {
      isValid: fileType !== 'other',
      fileType: fileType,
      stats: fileStats,
      issues: []
    };

    // Additional validation based on file type
    if (fileType === 'fasta') {
      // Add FASTA-specific validation
      const content = fs.readFileSync(filePath, 'utf8');
      const sequences = content.split('>').filter((seq) => seq.trim());

      validationResult.sequences = sequences.length;

      if (sequences.length === 0) {
        validationResult.issues.push('No sequences found in FASTA file');
      }
    } else if (fileType === 'fastq') {
      // Add FASTQ-specific validation
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n').filter((line) => line.trim());

      validationResult.reads = Math.floor(lines.length / 4);

      if (lines.length % 4 !== 0) {
        validationResult.issues.push('FASTQ file format is incomplete');
      }
    }

    res.json(validationResult);
  } catch (error) {
    console.error('File validation error:', error);
    res.status(500).json({
      error: 'Validation failed',
      message: 'An error occurred during file validation'
    });
  }
});

module.exports = router;

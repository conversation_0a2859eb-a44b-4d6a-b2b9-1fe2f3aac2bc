#!/usr/bin/env node

/**
 * 兼容性检查命令行工具
 * 
 * 使用方法：
 * node scripts/check-compatibility.js [--fix] [--verbose]
 * 
 * 参数：
 * --fix: 自动修复可修复的问题
 * --verbose: 显示详细信息
 */

const { fullCompatibilityCheck } = require('../utils/compatibility');

async function main() {
  const args = process.argv.slice(2);
  const shouldFix = args.includes('--fix');
  const verbose = args.includes('--verbose');

  console.log('🔍 Quantix 支付系统兼容性检查工具');
  console.log('='.repeat(50));

  if (shouldFix) {
    process.env.AUTO_FIX_COMPATIBILITY = 'true';
    console.log('🔧 自动修复模式已启用');
  }

  try {
    const result = await fullCompatibilityCheck();
    
    if (verbose) {
      console.log('\n📊 详细检查结果:');
      console.log(JSON.stringify(result, null, 2));
    }

    if (result.compatible) {
      console.log('\n✅ 系统兼容性检查通过！');
      process.exit(0);
    } else {
      console.log('\n❌ 发现兼容性问题');
      
      if (!shouldFix) {
        console.log('\n💡 提示: 使用 --fix 参数尝试自动修复');
      }
      
      process.exit(1);
    }
    
  } catch (error) {
    console.error('\n❌ 兼容性检查失败:', error.message);
    if (verbose) {
      console.error('详细错误信息:', error);
    }
    process.exit(1);
  }
}

main();

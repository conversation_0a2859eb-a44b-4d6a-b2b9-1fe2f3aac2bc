const request = require('supertest');
const app = require('../index');

describe('Basic API Tests', () => {
  describe('Health Check', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      expect(response.body.status).toBe('OK');
      expect(response.body.timestamp).toBeTruthy();
      expect(response.body.uptime).toBeGreaterThanOrEqual(0);
    });
  });

  describe('404 Handler', () => {
    it('should return 404 for non-existent routes', async () => {
      const response = await request(app)
        .get('/api/non-existent-route')
        .expect(404);

      expect(response.body.error).toBe('Not Found');
    });
  });

  describe('CORS Headers', () => {
    it('should include CORS headers', async () => {
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      expect(response.headers['access-control-allow-origin']).toBeTruthy();
    });
  });

  describe('Security Headers', () => {
    it('should include security headers', async () => {
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      // Helmet should add security headers
      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['x-frame-options']).toBeTruthy();
    });
  });

  describe('Rate Limiting', () => {
    it('should apply rate limiting', async () => {
      // Make multiple requests quickly
      const requests = Array(10).fill().map(() =>
        request(app).get('/api/health')
      );

      const responses = await Promise.all(requests);

      // All should succeed for health endpoint (it's not heavily rate limited)
      responses.forEach(response => {
        expect([200, 429]).toContain(response.status);
      });
    });
  });
});

describe('Chat API Tests', () => {
  describe('GET /api/chat/suggestions', () => {
    it('should return suggested questions in Chinese', async () => {
      const response = await request(app)
        .get('/api/chat/suggestions?language=zh')
        .expect(200);

      expect(response.body.suggestions).toBeInstanceOf(Array);
      expect(response.body.suggestions.length).toBeGreaterThan(0);
      expect(response.body.language).toBe('zh');
    });

    it('should return suggested questions in English', async () => {
      const response = await request(app)
        .get('/api/chat/suggestions?language=en')
        .expect(200);

      expect(response.body.suggestions).toBeInstanceOf(Array);
      expect(response.body.suggestions.length).toBeGreaterThan(0);
      expect(response.body.language).toBe('en');
    });
  });

  describe('GET /api/chat/health', () => {
    it('should return AI service health status', async () => {
      const response = await request(app)
        .get('/api/chat/health')
        .expect(200);

      expect(response.body.status).toBeTruthy();
      expect(response.body.timestamp).toBeTruthy();
      expect(response.body.configuration).toBeTruthy();
    });
  });

  describe('POST /api/chat/public/message', () => {
    it('should handle public chat messages', async () => {
      const message = {
        message: 'Hello, what are proteins?',
        language: 'en'
      };

      const response = await request(app)
        .post('/api/chat/public/message')
        .send(message)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.question).toBe(message.message);
      expect(response.body.data.answer).toBeTruthy();
    });

    it('should reject empty messages', async () => {
      const message = {
        message: '',
        language: 'en'
      };

      const response = await request(app)
        .post('/api/chat/public/message')
        .send(message)
        .expect(400);

      expect(response.body.error).toBe('Validation failed');
    });

    it('should reject messages that are too long', async () => {
      const message = {
        message: 'a'.repeat(1001), // Exceeds 1000 character limit
        language: 'en'
      };

      const response = await request(app)
        .post('/api/chat/public/message')
        .send(message)
        .expect(400);

      expect(response.body.error).toBe('Validation failed');
    });
  });
});

describe('Error Handling', () => {
  describe('Validation Errors', () => {
    it('should handle validation errors properly', async () => {
      const invalidData = {
        message: '', // Empty message should fail validation
        language: 'invalid' // Invalid language should fail validation
      };

      const response = await request(app)
        .post('/api/chat/public/message')
        .send(invalidData)
        .expect(400);

      expect(response.body.error).toBe('Validation failed');
      expect(response.body.details).toBeInstanceOf(Array);
    });
  });

  describe('Content Type Errors', () => {
    it('should handle invalid content type', async () => {
      const response = await request(app)
        .post('/api/chat/public/message')
        .set('Content-Type', 'text/plain')
        .send('invalid data')
        .expect(400);

      // Should get a validation error or bad request
      expect([400, 415]).toContain(response.status);
    });
  });
});

describe('Authentication Tests', () => {
  describe('Protected Routes', () => {
    it('should reject requests without authentication token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .expect(401);

      expect(response.body.error).toBe('Access denied');
    });

    it('should reject requests with invalid authentication token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body.error).toBe('Access denied');
    });
  });
});

describe('File Upload Tests', () => {
  describe('Protected File Routes', () => {
    it('should require authentication for file upload', async () => {
      const response = await request(app)
        .post('/api/upload/files')
        .expect(401);

      expect(response.body.error).toBe('Access denied');
    });

    it('should require authentication for file listing', async () => {
      const response = await request(app)
        .get('/api/upload/files')
        .expect(401);

      expect(response.body.error).toBe('Access denied');
    });
  });
});

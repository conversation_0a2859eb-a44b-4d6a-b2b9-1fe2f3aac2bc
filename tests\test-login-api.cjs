const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testLoginAPI() {
  console.log('🔐 测试登录API');
  console.log('='.repeat(30));
  
  const loginData = {
    email: '<EMAIL>',
    password: 'fHadmin'
  };
  
  try {
    console.log('发送登录请求...');
    console.log('邮箱:', loginData.email);
    console.log('密码:', loginData.password);
    
    const response = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(loginData)
    });
    
    console.log('\n响应状态:', response.status);
    console.log('响应状态文本:', response.statusText);
    
    const result = await response.json();
    console.log('\n响应数据:');
    console.log(JSON.stringify(result, null, 2));
    
    if (response.ok) {
      console.log('\n✅ 登录成功！');
      console.log('用户角色:', result.user.role);
      console.log('Token存在:', !!result.token);
    } else {
      console.log('\n❌ 登录失败！');
      console.log('错误:', result.error);
      console.log('消息:', result.message);
    }
    
  } catch (error) {
    console.error('❌ 请求失败:', error.message);
  }
  
  process.exit();
}

testLoginAPI();

@echo off
REM Docker 开发环境快速启动脚本 (Windows)
REM 用途: 启动适合开发的Docker环境，支持热重载
REM 作者: Quantix 开发团队
REM 日期: 2025-06-19

setlocal enabledelayedexpansion

echo 🐳 Quantix Docker 开发环境启动脚本
echo ==================================================

REM 检查Docker是否安装
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker 未安装或未在PATH中
    echo 请安装Docker: https://docs.docker.com/get-docker/
    pause
    exit /b 1
)

REM 检查Docker Compose是否安装
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose 未安装或未在PATH中
    echo 请安装Docker Compose: https://docs.docker.com/compose/install/
    pause
    exit /b 1
)

REM 检查Docker是否运行
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker 服务未运行
    echo 请启动Docker Desktop
    pause
    exit /b 1
)

echo ✅ Docker 环境检查通过

REM 检查项目根目录
if not exist "package.json" (
    echo ❌ 请从项目根目录运行此脚本
    pause
    exit /b 1
)
if not exist "server" (
    echo ❌ 请从项目根目录运行此脚本
    pause
    exit /b 1
)
if not exist "src" (
    echo ❌ 请从项目根目录运行此脚本
    pause
    exit /b 1
)

echo ✅ 项目目录检查通过

REM 停止现有容器
echo 🛑 停止现有容器...
docker-compose -f docker/docker-compose.yml down 2>nul

REM 询问是否清理旧数据
set /p cleanup="是否清理旧的开发数据？(y/N): "
if /i "%cleanup%"=="y" (
    echo 🧹 清理开发数据...
    docker volume rm quantix_mysql_dev_data 2>nul
)

REM 构建镜像
echo 🏗️ 构建开发环境镜像...
docker-compose -f docker/docker-compose.yml -f docker/docker-compose.dev.yml build

if %errorlevel% neq 0 (
    echo ❌ 镜像构建失败
    pause
    exit /b 1
)

REM 启动开发环境
echo 🚀 启动开发环境...
docker-compose -f docker/docker-compose.yml -f docker/docker-compose.dev.yml up -d

if %errorlevel% neq 0 (
    echo ❌ 容器启动失败
    echo 查看错误日志: docker-compose -f docker/docker-compose.yml logs
    pause
    exit /b 1
)

echo ⏳ 等待服务启动...
timeout /t 15 /nobreak >nul

REM 检查服务状态
echo 🔍 检查服务状态...
docker-compose -f docker/docker-compose.yml ps

REM 等待数据库就绪
echo ⏳ 等待数据库就绪...
set /a counter=0
:wait_db
set /a counter+=1
docker-compose -f docker/docker-compose.yml exec -T database mysqladmin ping -h localhost --silent >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 数据库已就绪
    goto db_ready
)
if %counter% geq 30 (
    echo ❌ 数据库启动超时
    pause
    exit /b 1
)
timeout /t 2 /nobreak >nul
goto wait_db

:db_ready

REM 健康检查
echo 🏥 执行健康检查...

REM 检查后端健康
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:3001/api/health' -UseBasicParsing | Out-Null; Write-Host '✅ 后端服务健康' } catch { Write-Host '⚠️ 后端服务可能还在启动中' }"

REM 检查前端
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:5173' -UseBasicParsing | Out-Null; Write-Host '✅ 前端服务健康' } catch { Write-Host '⚠️ 前端服务可能还在启动中' }"

echo.
echo 🎉 开发环境启动完成！
echo ==================================================
echo 📋 服务信息:
echo   🌐 前端开发服务器: http://localhost:5173
echo   🔧 后端API服务器:  http://localhost:3001
echo   🗄️ 数据库:         localhost:3306
echo   📊 Nginx状态:      http://localhost/nginx-status
echo.
echo 🛠️ 开发工具:
echo   📝 查看日志: docker-compose -f docker/docker-compose.yml logs -f
echo   🔍 进入容器: docker-compose -f docker/docker-compose.yml exec [service] sh
echo   🛑 停止服务: docker-compose -f docker/docker-compose.yml down
echo.
echo 💡 开发特性:
echo   🔄 热重载: 前端和后端代码修改会自动重载
echo   🗄️ 数据持久化: 数据库数据保存在Docker卷中
echo   🐛 调试模式: 后端运行在调试模式
echo.
echo ⚠️ 注意: 这是开发环境配置，不适用于生产环境

REM 询问是否打开浏览器
set /p browser="是否打开浏览器？(Y/n): "
if /i not "%browser%"=="n" (
    start http://localhost:5173
)

echo.
echo 按任意键退出...
pause >nul

const fs = require('fs');
const path = require('path');

console.log('🔄 恢复数据库配置...');

const configPath = path.join(__dirname, 'config', 'database.js');

try {
  let content = fs.readFileSync(configPath, 'utf8');

  // 恢复为 alter: true
  const forceSync = 'await sequelize.sync({ force: true })';
  const alterSync = 'await sequelize.sync({ alter: true })';

  if (content.includes(forceSync)) {
    content = content.replace(forceSync, alterSync);
    content = content.replace(
      'console.log(\'📊 Database forcefully rebuilt with complete schema\')',
      'console.log(\'📊 Database synchronized with schema updates\')'
    );

    fs.writeFileSync(configPath, content);
    console.log('✅ 数据库配置已恢复为增量同步模式');
  } else {
    console.log('✅ 数据库配置无需恢复');
  }
} catch (error) {
  console.error('❌ 恢复数据库配置失败:', error.message);
}

const nodemailer = require('nodemailer');

// Create email transporter based on environment
const createTransporter = () => {
  // In development, use different strategies
  // if (process.env.NODE_ENV === 'development') {
  //   // Option 1: Use Ethereal Email for testing
  //   if (process.env.EMAIL_TEST_MODE === 'ethereal') {
  //     return nodemailer.createTransport({
  //       host: 'smtp.ethereal.email',
  //       port: 587,
  //       auth: {
  //         user: process.env.ETHEREAL_USER || '<EMAIL>',
  //         pass: process.env.ETHEREAL_PASS || 'ethereal.pass',
  //       },
  //     })
  //   }

  //   // Option 2: Use local SMTP server (like MailHog)
  //   if (process.env.EMAIL_TEST_MODE === 'local') {
  //     return nodemailer.createTransport({
  //       host: process.env.SMTP_HOST || 'localhost',
  //       port: parseInt(process.env.SMTP_PORT) || 1025,
  //       ignoreTLS: true,
  //     })
  //   }

  //   // Option 3: Skip email in development (current behavior)
  //   return null
  // }

  // Production configuration
  if (process.env.EMAIL_SERVICE === 'gmail') {
    return nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    });
  }

  // QQ邮箱配置
  if (process.env.EMAIL_SERVICE === 'qq' || process.env.EMAIL_USER?.includes('@qq.com')) {
    return nodemailer.createTransport({
      host: 'smtp.qq.com',
      port: 587,
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS // QQ邮箱授权码
      },
      tls: {
        rejectUnauthorized: false
      }
    });
  }

  // Custom SMTP configuration
  return nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT) || 587,
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER || process.env.EMAIL_USER,
      pass: process.env.SMTP_PASS || process.env.EMAIL_PASS
    }
  });
};

// Generate verification code
const generateVerificationCode = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Send email verification code
const sendVerificationCode = async (email, code, language = 'zh') => {
  const transporter = createTransporter();

  // In development mode without email service, just log the code
  if (!transporter) {
    console.log(`📧 Development mode: Verification code for ${email}: ${code}`);
    return true;
  }

  const messages = {
    zh: {
      subject: '蛋白质复合物分析平台 - 邮箱验证码',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #007bff;">蛋白质复合物分析平台</h2>
          <p>您好！</p>
          <p>您的邮箱验证码是：</p>
          <div style="background-color: #f8f9fa; padding: 20px; text-align: center; margin: 20px 0;">
            <h1 style="color: #007bff; font-size: 32px; margin: 0;">${code}</h1>
          </div>
          <p>验证码有效期为10分钟，请及时使用。</p>
          <p>如果您没有请求此验证码，请忽略此邮件。</p>
          <hr style="margin: 30px 0;">
          <p style="color: #6c757d; font-size: 12px;">
            此邮件由系统自动发送，请勿回复。<br>
            蛋白质复合物分析平台团队
          </p>
        </div>
      `
    },
    en: {
      subject: 'Protein Complex Analysis Platform - Email Verification Code',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #007bff;">Protein Complex Analysis Platform</h2>
          <p>Hello!</p>
          <p>Your email verification code is:</p>
          <div style="background-color: #f8f9fa; padding: 20px; text-align: center; margin: 20px 0;">
            <h1 style="color: #007bff; font-size: 32px; margin: 0;">${code}</h1>
          </div>
          <p>This code is valid for 10 minutes. Please use it promptly.</p>
          <p>If you did not request this verification code, please ignore this email.</p>
          <hr style="margin: 30px 0;">
          <p style="color: #6c757d; font-size: 12px;">
            This email is sent automatically by the system. Please do not reply.<br>
            Protein Complex Analysis Platform Team
          </p>
        </div>
      `
    }
  };

  const message = messages[language] || messages.en;

  const mailOptions = {
    from: `"蛋白质复合物分析平台" <${process.env.EMAIL_USER}>`,
    to: email,
    subject: message.subject,
    html: message.html
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log(`Verification code sent to ${email}`);
    return true;
  } catch (error) {
    console.error('Error sending email:', error);
    throw new Error('Failed to send verification email');
  }
};

// Send welcome email
const sendWelcomeEmail = async (email, name, language = 'zh') => {
  const transporter = createTransporter();

  // In development mode without email service, just log
  if (!transporter) {
    console.log(`📧 Development mode: Welcome email for ${email} (${name})`);
    return true;
  }

  const messages = {
    zh: {
      subject: '欢迎加入蛋白质复合物分析平台！',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #007bff;">欢迎加入蛋白质复合物分析平台！</h2>
          <p>亲爱的 ${name || '用户'}，</p>
          <p>感谢您注册我们的蛋白质复合物分析平台！您现在可以：</p>
          <ul>
            <li>上传基因数据文件进行分析</li>
            <li>查看详细的分析报告</li>
            <li>使用AI智能问答系统</li>
            <li>下载分析结果</li>
          </ul>
          <p>如果您有任何问题，请随时联系我们的客服团队。</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL}/dashboard"
               style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;">
              开始使用
            </a>
          </div>
          <hr style="margin: 30px 0;">
          <p style="color: #6c757d; font-size: 12px;">
            蛋白质复合物分析平台团队
          </p>
        </div>
      `
    },
    en: {
      subject: 'Welcome to Protein Complex Analysis Platform!',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #007bff;">Welcome to Protein Complex Analysis Platform!</h2>
          <p>Dear ${name || 'User'},</p>
          <p>Thank you for registering with our Protein Complex Analysis Platform! You can now:</p>
          <ul>
            <li>Upload genetic data files for analysis</li>
            <li>View detailed analysis reports</li>
            <li>Use our AI-powered Q&A system</li>
            <li>Download analysis results</li>
          </ul>
          <p>If you have any questions, please feel free to contact our support team.</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL}/dashboard"
               style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;">
              Get Started
            </a>
          </div>
          <hr style="margin: 30px 0;">
          <p style="color: #6c757d; font-size: 12px;">
            Protein Complex Analysis Platform Team
          </p>
        </div>
      `
    }
  };

  const message = messages[language] || messages.en;

  const mailOptions = {
    from: `"蛋白质复合物分析平台" <${process.env.EMAIL_USER}>`,
    to: email,
    subject: message.subject,
    html: message.html
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log(`Welcome email sent to ${email}`);
    return true;
  } catch (error) {
    console.error('Error sending welcome email:', error);
    return false;
  }
};

// Send analysis completion notification
const sendAnalysisCompleteEmail = async (email, applicationId, language = 'zh') => {
  const transporter = createTransporter();

  // In development mode without email service, just log
  if (!transporter) {
    console.log(`📧 Development mode: Analysis complete email for ${email} (${applicationId})`);
    return true;
  }

  const messages = {
    zh: {
      subject: '分析完成通知 - 蛋白质复合物分析平台',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #007bff;">分析完成通知</h2>
          <p>您好！</p>
          <p>您的分析申请 <strong>${applicationId}</strong> 已经完成处理。</p>
          <p>您现在可以查看分析结果和下载报告。</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL}/report/${applicationId}"
               style="background-color: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;">
              查看报告
            </a>
          </div>
          <hr style="margin: 30px 0;">
          <p style="color: #6c757d; font-size: 12px;">
            蛋白质复合物分析平台团队
          </p>
        </div>
      `
    },
    en: {
      subject: 'Analysis Complete - Protein Complex Analysis Platform',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #007bff;">Analysis Complete</h2>
          <p>Hello!</p>
          <p>Your analysis application <strong>${applicationId}</strong> has been completed.</p>
          <p>You can now view the analysis results and download the report.</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL}/report/${applicationId}"
               style="background-color: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;">
              View Report
            </a>
          </div>
          <hr style="margin: 30px 0;">
          <p style="color: #6c757d; font-size: 12px;">
            Protein Complex Analysis Platform Team
          </p>
        </div>
      `
    }
  };

  const message = messages[language] || messages.en;

  const mailOptions = {
    from: `"蛋白质复合物分析平台" <${process.env.EMAIL_USER}>`,
    to: email,
    subject: message.subject,
    html: message.html
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log(`Analysis complete email sent to ${email}`);
    return true;
  } catch (error) {
    console.error('Error sending analysis complete email:', error);
    return false;
  }
};

module.exports = {
  generateVerificationCode,
  sendVerificationCode,
  sendWelcomeEmail,
  sendAnalysisCompleteEmail
};

const { sequelize } = require('./config/database');
const User = require('./models/User');
const bcrypt = require('bcryptjs');

async function createInitialData() {
  try {
    console.log('🔧 创建初始管理员账户...');

    // 手动加密密码
    const hashedPassword = await bcrypt.hash('fHadmin', 10);

    // 创建管理员账户
    const [_adminUser, created] = await User.findOrCreate({
      where: { email: '<EMAIL>' },
      defaults: {
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Admin',
        lastName: 'User',
        organization: 'Quantix',
        role: 'superadmin',
        isEmailVerified: true,
        isActive: true
      }
    });

    if (created) {
      console.log('✅ 管理员账户创建成功');
      console.log('   📧 邮箱: <EMAIL>');
      console.log('   🔑 密码: fHadmin');
      console.log('   🎭 角色: superadmin');
    } else {
      console.log('✅ 管理员账户已存在');
    }

    console.log('🎉 初始数据创建完成');

  } catch (error) {
    console.error('❌ 创建初始数据失败:', error);
    console.error('详细错误:', error.message);
  } finally {
    await sequelize.close();
    process.exit(0);
  }
}

// 等待数据库连接建立
setTimeout(() => {
  createInitialData();
}, 2000);

# 🚀 Scripts 目录

这个目录包含了 Quantix 项目的各种脚本文件，用于启动、测试和部署。

## 📋 脚本列表

### 🔧 启动脚本

#### `start.bat` / `start.sh`
**用途**: 启动开发环境  
**平台**: Windows / Linux/macOS  
**功能**:
- 同时启动前端和后端开发服务器
- 自动检查依赖
- 提供实时日志输出

```bash
# Windows
./scripts/start.bat

# Linux/macOS
./scripts/start.sh
```

#### `start-production.bat`
**用途**: 启动生产环境  
**平台**: Windows  
**功能**:
- 构建前端生产版本
- 启动生产模式后端
- 优化性能配置

```bash
./scripts/start-production.bat
```

### 🐳 Docker 脚本

#### `start-docker.bat` / `start-docker.sh`
**用途**: 使用 Docker 启动完整环境  
**平台**: Windows / Linux/macOS  
**功能**:
- 构建 Docker 镜像
- 启动容器化服务
- 配置网络和存储

```bash
# Windows
./scripts/start-docker.bat

# Linux/macOS
./scripts/start-docker.sh
```

### 🧪 测试脚本

#### `test-complete-system.bat`
**用途**: 运行完整系统测试  
**平台**: Windows  
**功能**:
- 端到端测试
- API 测试
- 功能验证

```bash
./scripts/test-complete-system.bat
```

#### `test-initialization.bat`
**用途**: 测试系统初始化流程  
**平台**: Windows  
**功能**:
- 验证初始化API
- 测试管理员创建
- 检查数据库状态

```bash
./scripts/test-initialization.bat
```

### 🔍 验证脚本

#### `verify-services.ps1`
**用途**: 验证服务状态  
**平台**: Windows (PowerShell)  
**功能**:
- 检查前端服务状态
- 检查后端服务状态
- 验证数据库连接
- 显示系统健康状态

```powershell
./scripts/verify-services.ps1
```

#### `quick-test.bat`
**用途**: 快速功能测试  
**平台**: Windows  
**功能**:
- 基础功能验证
- 快速健康检查

```bash
./scripts/quick-test.bat
```

## 🛠️ 使用指南

### 首次使用
1. **开发环境**: 使用 `start.bat` 或 `start.sh`
2. **Docker环境**: 使用 `start-docker.bat` 或 `start-docker.sh`
3. **验证服务**: 使用 `verify-services.ps1`

### 日常开发
1. **启动开发环境**: `./scripts/start.sh`
2. **运行测试**: `./scripts/test-complete-system.bat`
3. **验证功能**: `./scripts/verify-services.ps1`

### 生产部署
1. **构建生产版本**: `./scripts/start-production.bat`
2. **Docker部署**: `./scripts/start-docker.sh`
3. **系统验证**: `./scripts/verify-services.ps1`

## 📝 脚本说明

### 权限要求
- **Linux/macOS**: 确保脚本有执行权限
  ```bash
  chmod +x scripts/*.sh
  ```
- **Windows**: 以管理员身份运行 PowerShell 脚本

### 环境要求
- **Node.js**: 18+ 
- **npm**: 8+
- **Docker**: 20+ (Docker脚本)
- **PowerShell**: 5+ (Windows验证脚本)

### 端口配置
- **前端**: http://localhost:5173
- **后端**: http://localhost:3001
- **数据库**: SQLite (本地文件)

## 🔧 自定义脚本

### 创建新脚本
1. 在 `scripts/` 目录创建文件
2. 添加适当的文件头注释
3. 设置执行权限
4. 更新此 README

### 脚本模板
```bash
#!/bin/bash
# 脚本名称: my-script.sh
# 用途: 描述脚本功能
# 作者: 开发者名称
# 日期: YYYY-MM-DD

set -e  # 遇到错误时退出

echo "🚀 开始执行脚本..."

# 脚本逻辑
# ...

echo "✅ 脚本执行完成"
```

## 🐛 故障排除

### 常见问题
1. **权限错误**: 确保脚本有执行权限
2. **端口占用**: 检查端口 5173 和 3001 是否被占用
3. **依赖缺失**: 运行 `npm install` 安装依赖
4. **Docker问题**: 确保 Docker 服务正在运行

### 调试技巧
1. **查看日志**: 脚本会输出详细日志
2. **分步执行**: 手动执行脚本中的命令
3. **检查服务**: 使用 `verify-services.ps1` 检查状态

## 📞 支持

如果遇到脚本相关问题，请：
1. 检查此 README 的故障排除部分
2. 查看脚本输出的错误信息
3. 确认环境要求已满足
4. 联系开发团队获取支持

---

**维护**: Quantix 开发团队  
**更新**: 2025-06-19

<template>
  <div class="register-container">
    <div class="register-card">
      <h2>{{ $t('register') }}</h2>
      <form @submit.prevent="handleRegister">
        <div v-if="errorMessage" class="error-message">
          {{ errorMessage }}
        </div>
        <div v-if="successMessage" class="success-message">
          {{ successMessage }}
        </div>
        <div class="form-group">
          <label for="email">{{ $t('email') }}:</label>
          <input type="email" id="email" v-model="email" :disabled="loading" required />
        </div>
        <div class="form-group">
          <label for="password">{{ $t('password') }}:</label>
          <input
            type="password"
            id="password"
            v-model="password"
            :disabled="loading"
            required
            minlength="6"
          />
          <small class="form-text">密码至少6位，包含大小写字母和数字</small>
        </div>
        <div class="form-group">
          <label for="confirmPassword">{{ $t('confirmPassword') }}:</label>
          <input
            type="password"
            id="confirmPassword"
            v-model="confirmPassword"
            :disabled="loading"
            required
          />
        </div>
        <button type="submit" class="btn-primary" :disabled="loading">
          <span v-if="loading">{{ $t('registering') || '注册中...' }}</span>
          <span v-else>{{ $t('register') }}</span>
        </button>
      </form>
      <p class="login-link">
        {{ $t('haveAccount') }}<a href="#" @click.prevent="goToLogin">{{ $t('loginNow') }}</a>
      </p>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/stores/counter'

export default defineComponent({
  name: 'RegisterView',
  setup() {
    const email = ref('')
    const password = ref('')
    const confirmPassword = ref('')
    const router = useRouter()
    const { t } = useI18n()
    const userStore = useUserStore()
    const loading = ref(false)
    const errorMessage = ref('')
    const successMessage = ref('')

    const validatePassword = (password: string) => {
      const minLength = password.length >= 6
      const hasUpper = /[A-Z]/.test(password)
      const hasLower = /[a-z]/.test(password)
      const hasNumber = /\d/.test(password)

      return minLength && hasUpper && hasLower && hasNumber
    }

    const handleRegister = async () => {
      errorMessage.value = ''
      successMessage.value = ''

      // 验证输入
      if (!email.value || !password.value || !confirmPassword.value) {
        errorMessage.value = '请填写所有字段'
        return
      }

      if (password.value !== confirmPassword.value) {
        errorMessage.value = '两次输入的密码不一致'
        return
      }

      if (!validatePassword(password.value)) {
        errorMessage.value = '密码必须至少6位，包含大小写字母和数字'
        return
      }

      loading.value = true

      try {
        const result = await userStore.register({
          email: email.value,
          password: password.value,
          firstName: '',
          lastName: '',
          confirmPassword: confirmPassword.value,
        })

        if (result.success) {
          successMessage.value = '注册成功！请查收邮箱验证码'
          // 保存邮箱到本地存储，用于验证码页面
          localStorage.setItem('pendingEmail', email.value)
          setTimeout(() => {
            router.push('/captcha-input')
          }, 2000)
        } else {
          errorMessage.value = result.error || '注册失败'
        }
      } catch (error) {
        errorMessage.value = '网络错误，请稍后重试'
      } finally {
        loading.value = false
      }
    }

    const goToLogin = () => {
      router.push('/login')
    }

    return {
      email,
      password,
      confirmPassword,
      loading,
      errorMessage,
      successMessage,
      handleRegister,
      goToLogin,
    }
  },
})
</script>

<style scoped>
.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(to right, #ece9e6, #ffffff); /* 渐变背景 */
  font-family: 'Arial', sans-serif;
}

.register-card {
  background-color: #fff;
  padding: 40px;
  border-radius: 10px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  text-align: center;
  width: 100%;
  max-width: 400px;
}

h2 {
  color: #333;
  margin-bottom: 20px;
  font-size: 28px;
}

.form-group {
  margin-bottom: 20px;
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #555;
  font-weight: bold;
}

.form-group input {
  width: calc(100% - 20px);
  padding: 12px 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
}

.btn-primary {
  background-color: #28a745;
  color: white;
  padding: 12px 25px;
  border: none;
  border-radius: 5px;
  font-size: 18px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  width: 100%;
}

.btn-primary:hover {
  background-color: #218838;
}

.login-link {
  margin-top: 25px;
  color: #777;
}

.login-link a {
  color: #007bff;
  text-decoration: none;
  font-weight: bold;
}

.login-link a:hover {
  text-decoration: underline;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
  border: 1px solid #f5c6cb;
}

.success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
  border: 1px solid #c3e6cb;
}

.btn-primary:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.form-group input:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.form-text {
  color: #6c757d;
  font-size: 12px;
  margin-top: 5px;
  display: block;
}
</style>

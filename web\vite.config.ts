import { fileURLToPath, URL } from "node:url";

import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
import vueDevTools from "vite-plugin-vue-devtools";

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue(), vueJsx(), vueDevTools()],
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
    },
  },
  server: {
    allowedHosts: ["sanbgi.vicp.cc"],
    host: "0.0.0.0", // 监听所有网络接口
    port: 5173, // 明确指定端口
    proxy: {
      "/api": {
        target: "http://localhost:3001",
        changeOrigin: true,
        // 不重写路径，保持/api前缀
      },
    },
  },
});

{"name": "quantix-backend", "version": "1.0.0", "description": "Backend API for Protein Complex Analysis Platform", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:watch": "jest --watch --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:coverage": "jest --coverage --detect<PERSON><PERSON><PERSON><PERSON><PERSON>", "setup": "node scripts/setup-database.js", "init-db": "node scripts/init-database.js", "check-compatibility": "node scripts/check-compatibility.js", "check-compatibility:fix": "node scripts/check-compatibility.js --fix", "upgrade-payment": "node scripts/upgrade-payment-system.js", "upgrade-payment:preview": "node scripts/upgrade-payment-system.js --dry-run", "upgrade-payment:force": "node scripts/upgrade-payment-system.js --force"}, "keywords": ["protein", "bioinformatics", "analysis", "api", "nodejs", "express"], "author": "Quantix Team", "license": "MIT", "dependencies": {"alipay-sdk": "^4.14.0", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.19.2", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1", "mysql2": "^3.14.1", "nodemailer": "^7.0.3", "openai": "^5.6.0", "qrcode": "^1.5.4", "sequelize": "^6.37.7", "sqlite3": "^5.1.7", "stripe": "^18.2.1", "uuid": "^11.1.0", "wechatpay-node-v3": "^2.2.1"}, "devDependencies": {"eslint": "^9.29.0", "eslint-config-node": "^4.1.0", "eslint-plugin-node": "^11.1.0", "jest": "^30.0.2", "nodemon": "^3.1.10", "supertest": "^7.1.1"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["**/*.js", "!**/node_modules/**", "!**/coverage/**", "!**/uploads/**"], "testMatch": ["**/tests/**/*.test.js"]}, "engines": {"node": ">=20.18.1", "npm": ">=9.0.0"}, "overrides": {"glob": "^10.4.5", "inflight": "npm:@isaacs/inflight@^1.0.6", "rimraf": "^5.0.10", "superagent": "^9.0.2", "eslint": "^9.29.0"}}